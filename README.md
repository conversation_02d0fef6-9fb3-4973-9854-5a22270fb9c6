# NavidaLit

Eine lit-html basierte Version der NavidaWeb App.

## Projektstruktur

### Hauptkomponenten
- `index.html`: Einstiegspunkt der Anwendung mit Grundstruktur und CSS-Einbindungen
- `index.js`: Hauptdatei für die Initialisierung der App, Router-Konfiguration und Event-Handling
- `router.js`: Definiert die Routen und Navigation der App mit Navigo
- `utils.js`: Hilfsfunktionen und globaler App-State mit appStorage

### Verzeichnisse
- `/src`: Enthält die Hauptkomponenten und Seitenvorlagen
  - `/healthgoals`: Gesundheitsziele-Komponenten
    - `/challenges`: Challenge-spezifische Komponenten
    - `/trainings`: Trainingskomponenten für verschiedene Aktivitäten
  - `/features.js`: Funktionsübersicht
  - `/homescreen.js`: Startbildschirm
  - `/welcome.js`: Begrüßungsbildschirm
  - `/bottomNavigation.js`: Untere Navigationsleiste
- `/webcomponents`: Wiederverwendbare UI-Komponenten (Cards, Buttons, etc.)
- `/helpers`: Hilfsfunktionen für spezifische Aufgaben
- `/svg`: SVG-Icons und -Grafiken
- `/img`: Bilder für die Anwendung

## Technologie-Stack

- **lit-html**: Templating-Bibliothek für effizientes DOM-Rendering
- **Navigo**: Routing-Bibliothek für clientseitige Navigation
- **Capacitor**: Framework für die Erstellung von nativen Apps aus Web-Apps
- **Parcel**: Bundler für das Projekt

## Architektur und Muster

### Komponenten-Struktur
Die App verwendet ein komponentenbasiertes Design mit lit-html Templates. Jede Komponente ist in der Regel als Funktion implementiert, die ein lit-html Template zurückgibt.

```javascript
export const myComponent = (props) => html`
  <div class="my-component">
    <!-- Komponenten-Inhalt -->
  </div>
`;
```

### Routing
Die Navigation erfolgt über Navigo. Routen werden in `router.js` definiert und über `data-navigate`-Attribute in HTML-Elementen oder programmatisch ausgelöst.

```javascript
router.on("/path", () => {
  updateUI(templateFunction(), options);
});
```

### State-Management
Der App-State wird über `appStorage` in `utils.js` verwaltet, der Persistenz im localStorage bietet:

```javascript
// Daten abrufen
const username = appStorage.userName;

// Daten setzen
appStorage.userName = "Max";

// App-Verbindungen verwalten
appStorage.setAppConnection("komoot", true);

// Gesundheitsziele aktivieren
appStorage.activateHealthGoal("fitUmgebung");

// Challenge-Daten aktualisieren
appStorage.updateChallenge("lockereWanderung", { started: true });
```

Der `appStorage` bietet folgende Vorteile:
- Automatische Persistenz im localStorage
- Getter und Setter für häufig verwendete Daten
- Methoden für spezifische Datenoperationen
- Konsistente Datenstruktur

### Animationen
Seitenübergänge verwenden CSS-Animationen und werden über die `exitWithSlide`-Funktion gesteuert.

## Entwicklungsrichtlinien

1. **Komponenten-Struktur**: Neue UI-Elemente als wiederverwendbare Komponenten in `/webcomponents` erstellen
2. **Namenskonventionen**: 
   - Komponenten: camelCase (z.B. `buttonStandard.js`)
   - Templates: camelCase mit "template" Präfix (z.B. `templateHomescreen`)
3. **Routing**: Neue Routen in `router.js` hinzufügen und mit `updateUI` verknüpfen
4. **Styling**: CSS-Klassen verwenden, die in den CSS-Dateien definiert sind
5. **Icons**: SVG-Icons in `/svg` ablegen und importieren
6. **Bilder**: Bilder in `/img` mit entsprechenden Unterordnern organisieren
7. **State-Management**: `appStorage` für persistente Daten verwenden

## Build und Deployment

### Entwicklung
```bash
npm start
```
Startet einen Entwicklungsserver mit Parcel (Hot-Reloading aktiviert)

### Build
```bash
npm run build
```
Erstellt eine produktionsreife Version in `dist-build`

### iOS-Deployment
```bash
npm run ios:all
```
Baut die App und öffnet sie in Xcode für iOS-Deployment

## Capacitor-Integration

Die App ist für die Verwendung mit Capacitor konfiguriert, um native Funktionen auf iOS zu nutzen. Die Konfiguration befindet sich in `capacitor.config.json`.
