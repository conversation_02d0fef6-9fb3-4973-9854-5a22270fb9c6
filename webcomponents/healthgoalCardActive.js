import { html } from "lit-html";
import { Tag } from "./tag.js";
import { progressBar } from "./progressBar.js";
import "./healthgoalCard_active2.css"; // Keep CSS filename for now to avoid breaking styles

/**
 * Renders an active health goal card with various optional elements
 * 
 * @param {Object} props - Component properties
 * @param {string} props.cardImage - Path to the card image
 * @param {string} [props.tagCategory=""] - Category text for the tag
 * @param {string} [props.tagIcon=""] - Icon for the tag
 * @param {string} [props.tagColor=""] - Background color for the tag
 * @param {string} [props.tagTextColor=""] - Text color for the tag
 * @param {string} props.healthgoalTitle - Title of the health goal
 * @param {string} [props.healthgoalCoop=""] - Cooperation partner text
 * @param {string} [props.pillText=""] - Text for the pill element
 * @param {string} [props.pillColor=""] - Color for the pill element
 * @param {string} [props.goalinfoIcon1=""] - Icon for the first goal info
 * @param {string} [props.goalinfoText1=""] - Text for the first goal info
 * @param {string} [props.goalinfoIcon2=""] - Icon for the second goal info
 * @param {string} [props.goalinfoText2=""] - Text for the second goal info
 * @param {number} [props.completedChallenges=0] - Number of completed challenges
 * @param {number} [props.totalChallenges=0] - Total number of challenges
 * @param {boolean} [props.elevated=false] - Whether to use elevated styling
 * @returns {TemplateResult} The health goal card template
 */
export const healthgoalCardActive = ({
  cardImage,
  tagCategory = "",
  tagIcon = "",
  tagColor = "",
  tagTextColor = "",
  healthgoalTitle,
  healthgoalCoop = "",
  pillText = "",
  pillColor = "",
  goalinfoIcon1 = "",
  goalinfoText1 = "",
  goalinfoIcon2 = "",
  goalinfoText2 = "",
  completedChallenges = 0,
  totalChallenges = 0,
  elevated = false,
}) => {
  // Dynamic classes for the main container class if elevated is used
  const containerClass = `
    healthgoal-card-active
    ${elevated ? "elevated" : ""} 
  `.trim();

  return html`
    <div class="${containerClass}">
      <img
        src="${cardImage}"
        alt="Health goal image"
        class="card-image-active"
      />
      <!-- Pill (optional) -->
      ${pillText
        ? html`
            <div
              class="pill active-pill"
              style="background-color: var(${pillColor})"
            >
              <p class="caption semibold">${pillText}</p>
            </div>
          `
        : ""}
      <div class="healthgoal-card-content">
        <!-- Tag (optional) -->
        ${tagCategory || tagIcon || tagColor || tagTextColor
          ? Tag(tagColor, tagIcon, tagCategory, tagTextColor)
          : ""}
        <!-- Title -->
        <h3 class="healthgoal-title semibold tag-text">${healthgoalTitle}</h3>
        <!-- Coop Text (optional) -->
        ${healthgoalCoop
          ? html`<p class="healthgoal-coop caption tag-text">
              ${healthgoalCoop}
            </p>`
          : ""}
        <!-- Progress Bar (optional) -->
        ${totalChallenges > 0
          ? progressBar({ completedChallenges, totalChallenges })
          : ""}
        <!-- Goal Info -->
        <div class="goal-info-active">
          ${goalinfoIcon1 && goalinfoText1
            ? html`
                <div class="info-item">
                  <img
                    src="${goalinfoIcon1}"
                    alt="Info icon 1"
                    class="info-icon"
                  />
                  <span class="info-text small tag-text">${goalinfoText1}</span>
                </div>
              `
            : ""}
          ${goalinfoIcon2 && goalinfoText2
            ? html`
                <div class="info-item">
                  <img
                    src="${goalinfoIcon2}"
                    alt="Info icon 2"
                    class="info-icon"
                  />
                  <span class="info-text small tag-text">${goalinfoText2}</span>
                </div>
              `
            : ""}
        </div>
      </div>
    </div>
  `;
};

// For backward compatibility
export const healthgoalCard_active = healthgoalCardActive;
