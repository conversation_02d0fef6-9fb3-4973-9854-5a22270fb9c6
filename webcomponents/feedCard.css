.feed-card {
  color: var(--cards-text);
  padding: 0 0 16px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  border-radius: 8px;
  width: 100%;
  overflow: hidden;
  /* Elevation/8 z */
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08),
    0px 2px 24px 0px rgba(0, 0, 0, 0.08);
}

.feed-card .content-container {
  width: 100%;
}

.feed-card .content-container > * {
  margin: 12px 0;
}

.feed-card .image-container {
  width: 100%;
  margin-left: -24px;
  margin-right: -24px;
}

.feed-card .image {
  height: 110px;
}

.feed-card .image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.feed-card .content-container {
  padding-left: 24px;
  padding-right: 24px;
}

.feed-card .content-container > p.small {
  display: -webkit-box; /* Erforderlich für Webkit-basierte Browser */
  line-clamp: 4; /* Begrenzung auf 4 Zeilen */
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical; /* Festlegen der vertikalen Ausrichtung */
  overflow: hidden;
  text-overflow: ellipsis;
}

.feed-card .content-container > p:last-child {
  margin-bottom: 0;
}

.feed-card .category {
  color: var(--icon-black);
  display: flex;
  gap: 8px;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
