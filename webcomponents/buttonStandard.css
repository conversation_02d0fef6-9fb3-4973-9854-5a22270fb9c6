/* Basis-Styles */
.button-standard {
  display: inline-flex;
  padding: 14px 16px 12px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  text-decoration: none; /* Entfernt die Unterstreichung bei Links */
  color: var(--button-text); /* Übernimmt die Textfarbe */
  transition: background-color 0.3s ease-in-out, opacity 0.3s ease-in-out,
    border-color 0.3s ease-in-out;
}

/* Primary Button */
.button-standard.primary {
  background: var(--button-primary-background-default);
  width: 100%;
}

.button-standard.primary:hover:not(.disabled) {
  background: var(--button-primary-background-pressed);
}

.button-standard.primary.disabled {
  opacity: 0.4;
  background: var(--button-primary-background-disabled);
  border-color: var(
    --button-primary-background-disabled
  ); /* Border und Background gleichsetzen */
  pointer-events: none; /* Deaktiviert Mausinteraktionen */
}

/* Secondary Button */
.button-standard.secondary {
  border: 2px solid var(--button-secondary-on-backgound-default);
}

.button-standard.secondary:hover:not(.disabled) {
  border-color: var(--button-secondary-on-backgound-pressed);
  background: var(--button-secondary-background-pressed);
}

.button-standard.secondary.disabled {
  opacity: 0.4;
  background: var(
    --button-secondary-on-backgound-disabled
  ); /* Hintergrundfarbe im Disabled-Modus */
  border-color: var(
    --button-secondary-on-backgound-disabled
  ); /* Border und Background gleichsetzen */
  pointer-events: none; /* Deaktiviert Mausinteraktionen */
}

/* Tertiary Button */
.button-standard.tertiary {
  padding: 0;
}

.button-standard.tertiary:hover:not(.disabled) {
  background: var(--button-tertiary-background-pressed);
}

.button-standard.tertiary.disabled {
  opacity: 0.4;
  background: var(
    --button-tertiary-background-pressed
  ); /* Hintergrundfarbe im Disabled-Modus */
  border-color: var(
    --button-tertiary-background-pressed
  ); /* Border und Background gleichsetzen */
  pointer-events: none; /* Deaktiviert Mausinteraktionen */
}

/* Danger (Rot) */
.button-standard.danger.primary {
  background: var(--button-danger-background-default);
}

.button-standard.danger.primary:hover:not(.disabled) {
  background: var(--button-danger-background-pressed);
}

.button-standard.danger.primary.disabled {
  opacity: 0.4;
  background: var(
    --button-danger-background-default
  ); /* Hintergrundfarbe im Disabled-Modus */
  border-color: var(
    --button-danger-background-default
  ); /* Border und Background gleichsetzen */
  pointer-events: none; /* Deaktiviert Mausinteraktionen */
}
