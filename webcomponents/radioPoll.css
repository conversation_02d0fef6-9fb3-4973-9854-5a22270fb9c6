/* Container for the radio button and label */
.radio-option {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1 0 0;
}

/* Custom radio button */
.radio-option input[type="radio"] {
  appearance: none; /* Remove default styling */
  width: 24px;
  height: 24px;
  border: 2px solid var(--radio-border-color);
  border-radius: 50%;
  outline: none;
  cursor: pointer;
  position: relative;
  padding: 0;
}

/* Inner circle for the selected state */
.radio-option input[type="radio"]:checked::before {
  content: "";
  width: 12px;
  height: 12px;
  background-color: var(--radio-border-color);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Label styling */
label.radio-option {
  min-height: 24px;
  align-self: stretch;
  font-size: 1rem;
  font-style: normal;
  font-weight: 400;
  color: var(--radio-text-color);
  line-height: 1.36rem;
  letter-spacing: 0.15px;
  cursor: pointer;
  padding: 16px 24px;
}

label.radio-option span {
  padding-top: 2px;
}

/* Dividers */
.divider {
  width: calc(
    100% - 48px
  ); /* Full width minus left/right padding (24px each) */
  border-top: 1px solid var(--divider-default, #dfe3e6); /* Divider color and thickness */
  margin: 0 auto; /* Zentriert die Linie */
}
