import { html } from "lit-html";
import "./buttonStandard.css";
import { logError, ErrorLevel } from "../utils.js";

/**
 * Valid button variants
 * @readonly
 * @enum {string}
 */
const ButtonVariant = {
  PRIMARY: "primary",
  SECONDARY: "secondary",
  TERTIARY: "tertiary"
};

/**
 * Standard button component that can be rendered as a button or link
 *
 * @param {Object} props - Component properties
 * @param {string} [props.text="Button"] - Button text content
 * @param {ButtonVariant} [props.variant="primary"] - Button style variant
 * @param {boolean} [props.disabled=false] - Whether the button is disabled
 * @param {boolean} [props.danger=false] - Whether to use danger styling (red)
 * @param {string|null} [props.link=null] - Optional navigation link (renders as <a> tag)
 * @param {string|null} [props.id=null] - Optional ID for the button element
 * @param {string|null} [props.ariaLabel=null] - Optional aria-label for accessibility
 * @param {Function|null} [props.onClick=null] - Optional click handler
 * @returns {TemplateResult} The button template
 */
export const buttonStandard = ({
  text = "Button",
  variant = ButtonVariant.PRIMARY,
  disabled = false,
  danger = false,
  link = null,
  id = null,
  ariaLabel = null,
  onClick = null
}) => {
  try {
    // Validate variant
    if (![ButtonVariant.PRIMARY, ButtonVariant.SECONDARY, ButtonVariant.TERTIARY].includes(variant)) {
      logError(`Invalid button variant: ${variant}. Using default.`, ErrorLevel.WARNING);
      variant = ButtonVariant.PRIMARY;
    }

    // Generate dynamic classes based on parameters
    const buttonClass = `
      button-standard
      ${variant}
      ${disabled ? "disabled" : ""}
      ${danger ? "danger" : ""}
    `.trim().replace(/\s+/g, ' ');

    // Render as link if link is provided and button is not disabled
    if (link && !disabled) {
      console.log(`[buttonStandard] Creating link with data-navigate="${link}"`);
      return html`
        <a
          href="${link}"
          data-navigate="${link}"
          id="${id || ''}"
          class="${buttonClass}"
          aria-label="${ariaLabel || text}"
          @click=${(e) => {
            console.log(`[buttonStandard] Link clicked: ${link}`);
            if (onClick) onClick(e);
          }}
        >
          ${text}
        </a>
      `;
    }

    // Otherwise render as button
    return html`
      <button
        id="${id || ''}"
        class="${buttonClass}"
        aria-label="${ariaLabel || text}"
        ?disabled=${disabled}
        @click=${onClick || null}
      >
        ${text}
      </button>
    `;
  } catch (error) {
    logError("Error rendering button component", ErrorLevel.ERROR, error);
    // Fallback rendering in case of error
    return html`<button class="button-standard primary">${text || "Button"}</button>`;
  }
};
