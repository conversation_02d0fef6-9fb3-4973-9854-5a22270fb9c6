import { html } from "lit-html";
import "./infoCardSlider.css";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import iconArrowRight from "../svg/icons/ArrowRight.svg";

export const infoCardSlider = ({ icoPath, title, text, linkText }) => html`
  <div class="card info-card-mi-d-c">
    <img src="${icoPath}" alt="Icon" />
    <div>
      <div>
        <p class="small">${title}</p>
        <!-- Unsafe nutzen aufgrund manuellem Umbruch -->
        <p class="semibold">${unsafeHTML(text)}</p>
      </div>
      <div class="button-container">
        <span class="link-text">${linkText}</span>
        <img
          class="img-button"
          src="${iconArrowRight}"
          alt="Pfeil nach rechts Icon"
        />
      </div>
    </div>
  </div>
`;
