import { html } from "lit-html";
import "./buttonTextIcon.css";

/**
 * Button component with text and optional icon
 * 
 * @param {string} text - The button text
 * @param {string} [icon=null] - Path to the icon image (optional)
 * @param {string} [iconPosition="right"] - Position of the icon ("left" or "right")
 * @returns {TemplateResult} The button template
 */
export const buttonTextIcon = (text, icon = null, iconPosition = "right") => html`
  <button class="text-icon-button">
    ${icon && iconPosition === "left"
      ? html`<img class="icon-left" src="${icon}" alt="Icon" />`
      : ""}
    <span class="button-text">${text || "Continue"}</span>
    ${icon && iconPosition === "right"
      ? html`<img class="icon-right" src="${icon}" alt="Icon" />`
      : ""}
  </button>
`;
