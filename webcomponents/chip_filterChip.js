import { html } from "lit-html";
import "./chip_filterChip.css";
import iconCheck from "../svg/icons/icon_check.svg";

export function createChips(chipTexts, onFilterChange = null) {
  // Create an array of chip objects from the input string
  let chips = chipTexts.split(" ").map((text) => ({ text, active: false }));

  // Define the template for rendering chips
  const chipsTemplate = html`
    <div class="chip-container">
      ${chips.map(
        (chip, index) => html`
          <div
            class="chip ${chip.active ? "active" : ""}"
            @click=${() => toggleChip(index)}
          >
            ${chip.active
              ? html`<img src=${iconCheck} alt="check" class="chip-icon" />`
              : ""}
            <span>${chip.text}</span>
          </div>
        `
      )}
    </div>
  `;

  // Function to toggle the active state of a chip
  function toggleChip(index) {
    chips[index].active = !chips[index].active;
    const chipElements = document.getElementsByClassName("chip");
    chipElements[index].classList.toggle("active");

    // Add or remove the check icon based on the chip's active state
    if (chips[index].active) {
      chipElements[index].insertAdjacentHTML(
        "afterbegin",
        `<img src=${iconCheck} alt="check" class="chip-icon" />`
      );
    } else {
      chipElements[index].querySelector(".chip-icon")?.remove();
    }
    
    // Call the filter change callback if provided
    if (onFilterChange && typeof onFilterChange === 'function') {
      // Get active filters
      const activeFilters = chips
        .filter(chip => chip.active)
        .map(chip => chip.text);
      onFilterChange(activeFilters);
    }
  }

  // Return an object with the template and initialization function
  return {
    template: chipsTemplate,
    initialize: () => {
      console.log("Initializing chips");
      // Add initialization logic here if needed
    },
    getActiveFilters: () => chips.filter(chip => chip.active).map(chip => chip.text)
  };
}

// Log when the module is loaded
console.log("Chips module loaded");
