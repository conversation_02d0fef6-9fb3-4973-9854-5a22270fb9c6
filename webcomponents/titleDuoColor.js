import { html } from "lit-html";
import "./titleDuoColor.css";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js"; // Importiere unsafeHTML damit "" korrekt angezeigt werden oder andere &amp; HTML ASCII Zeichen

export const titleDuoColor = (darkText, contrastText = "", level = "h1") => {
  // Dynamisch das Tag basierend auf dem Parameter "level" setzen
  if (level === "h2") {
    return html`
      <h2 class="title-duo-color">
        <span class="dark-grn-text">${unsafeHTML(darkText)}</span>
        ${contrastText
          ? html`<span class="contrast-grn-text"
              >${unsafeHTML(contrastText)}</span
            >`
          : ""}
      </h2>
    `;
  }

  // Standardmäßig H1
  return html`
    <h1 class="title-duo-color">
      <span class="dark-grn-text">${unsafeHTML(darkText)}</span>
      ${contrastText
        ? html`<span class="contrast-grn-text"
            >${unsafeHTML(contrastText)}</span
          >`
        : ""}
    </h1>
  `;
};
