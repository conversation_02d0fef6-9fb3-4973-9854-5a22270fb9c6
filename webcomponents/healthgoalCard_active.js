import { html } from "lit-html";
import { Tag } from "./tag.js";
import { progressBar } from "./progressBar.js";
import "./healthgoalCard_active2.css";

export const healthgoalCard_active = ({
  cardImage,
  tagCategory = "",
  tagIcon = "",
  tagColor = "",
  tagTextColor = "",
  healthgoalTitle,
  healthgoalCoop = "",
  pillText = "",
  pillColor = "",
  goalinfoIcon1 = "",
  goalinfoText1 = "",
  goalinfoIcon2 = "",
  goalinfoText2 = "",
  completedChallenges = 0,
  totalChallenges = 0,
  elevated = false,
}) => {
  // Dynamische Klassen für die Hauptcontainerklasse, falls elevated genutzt wird
  const containerClass = `
    healthgoal-card-active
    ${elevated ? "elevated" : ""} 
  `.trim();

  return html`
    <div class="${containerClass}">
      <img
        src="${cardImage}"
        alt="Health goal image"
        class="card-image-active"
      />
      <!-- Pill (optional) -->
      ${pillText
        ? html`
            <div
              class="pill active-pill"
              style="background-color: var(${pillColor})"
            >
              <p class="caption semibold">${pillText}</p>
            </div>
          `
        : ""}
      <div class="healthgoal-card-content">
        <!-- Tag (optional) -->
        ${tagCategory || tagIcon || tagColor || tagTextColor
          ? Tag(tagColor, tagIcon, tagCategory, tagTextColor)
          : ""}
        <!-- Titel -->
        <h3 class="healthgoal-title semibold tag-text">${healthgoalTitle}</h3>
        <!-- Coop Text (optional) -->
        ${healthgoalCoop
          ? html`<p class="healthgoal-coop caption tag-text">
              ${healthgoalCoop}
            </p>`
          : ""}
        <!-- Progress Bar (optional) -->
        ${totalChallenges > 0
          ? progressBar({ completedChallenges, totalChallenges })
          : ""}
        <!-- Goal Info -->
        <div class="goal-info-active">
          ${goalinfoIcon1 && goalinfoText1
            ? html`
                <div class="info-item">
                  <img
                    src="${goalinfoIcon1}"
                    alt="Info icon 1"
                    class="info-icon"
                  />
                  <span class="info-text small tag-text">${goalinfoText1}</span>
                </div>
              `
            : ""}
          ${goalinfoIcon2 && goalinfoText2
            ? html`
                <div class="info-item">
                  <img
                    src="${goalinfoIcon2}"
                    alt="Info icon 2"
                    class="info-icon"
                  />
                  <span class="info-text small tag-text">${goalinfoText2}</span>
                </div>
              `
            : ""}
        </div>
      </div>
    </div>
  `;
};
