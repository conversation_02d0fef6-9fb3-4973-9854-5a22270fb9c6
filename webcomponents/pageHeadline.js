import { html } from "lit-html";
import "./pageHeadline.css";

/**
 * Page headline component with optional two-color title
 *
 * @param {Object} props - Component properties
 * @param {string} props.bgColor - Background color variable name
 * @param {string} props.title - Main title text
 * @param {string} [props.title2=""] - Optional second part of the title with different color
 * @returns {TemplateResult} The page headline template
 */
export const pageHeadline = ({ bgColor, title, title2 = "" }) => html`
  <div
    class="page-headline-container"
    style="background-color: var(--${bgColor}, #ffffff);"
  >
    <h1 class="${title2 ? 'headline-duo-color' : ''}">
      <span class="headline-part1">${title}</span>
      ${title2 ? html`<span class="headline-part2">${title2}</span>` : ''}
    </h1>
  </div>
`;
