.tab {
  overflow: hidden;
  display: flex;
  padding: 2px;
  align-items: flex-start;
  gap: 1px;
  align-self: stretch;
  border-radius: 8px;
  background: var(--aok-grn-8);
  flex-direction: row !important;
  margin: 24px;
  width: auto !important;
}
.tab button {
  all: unset;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: none;
  border: none;
  margin: 0;
  font-family: "AOK Buenos Aires Text";
  font-weight: 400;
  font-size: 1rem;
  letter-spacing: 0.15px;
  line-height: 1.35rem;
  color: white;
  cursor: pointer;
  outline: none;
  display: flex;
  padding: 5px 8px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex: 1 0 0;
  border-radius: 6px;
  cursor: pointer;
  transition: 0.3s;
}
.tab button:hover,
.tab button:focus {
  background-color: var(--aok-grn-7);
}
.tab button.active {
  background-color: white;
  color: var(--aok-grn-8);
}
.tabcontent {
  display: none;
  padding: 0;
  width: 100%;
}

.tabpadding {
  padding-bottom: 24px;
}

.tabpadding > div {
  padding-top: 24px;
  padding-bottom: 24px;
}

.firstcard-no-top-padding > div:first-child {
  margin-top: 0;
}
