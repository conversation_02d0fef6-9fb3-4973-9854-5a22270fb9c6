import { html } from "lit-html";
import "./featureCard.css";

export const featureCard = ({ imgPath, title, text, url }) => html`
  <a data-navigate="${url}">
    <div class="feature-card" style="background-color: #ffffff">
      <div class="image-container">
        <div class="image">
          <img src="${imgPath}" alt="Illustration" />
        </div>
      </div>
      <div class="content-container">
        <!-- Use Unsafe HTML to be able to render &shy; soft hyphens -->
        <p class="semibold">${title || "Titel"}</p>
        <p class="small">${text || "Text"}</p>
      </div>
    </div>
  </a>
`;
