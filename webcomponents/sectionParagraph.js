import { html } from "lit-html";
import "./sectionParagraph.css";

/**
 * Renders a section paragraph
 * 
 * @param {string} text - The paragraph text
 * @param {boolean} bold - Whether to make the text semibold
 * @param {boolean} noPadding - Whether to remove left and right padding
 * @returns {TemplateResult} The section paragraph template
 */
export const sectionParagraph = (text, bold = false, noPadding = false) => html`
  <p class="section-paragraph ${bold ? "semibold" : ""} ${noPadding ? "no-padding" : ""}">${text}</p>
`;
