import { html } from "lit-html";
import "./listItem.css";

/**
 * List item component with sub-items
 *
 * @param {Object[]} subItems - Array of sub-item configurations
 * @param {string} [subItems[].caption] - Optional caption for the sub-item
 * @param {string} [subItems[].text] - Optional text for the sub-item
 * @param {string} [subItems[].icon] - Optional icon path
 * @param {string} [subItems[].iconPosition="right"] - Position of the icon ("left" or "right")
 * @param {Function} [subItems[].onClick] - Optional click handler for the item
 * @param {TemplateResult} [subItems[].customContent] - Optional custom content to display on the right side
 * @returns {TemplateResult} The list item template
 */
export const listItem = (subItems) => html`
  <div class="listItem">
    ${subItems.map(
      (item, index) => html`
        <div class="listItemContainer" @click=${item.onClick || null} style="${item.onClick ? 'cursor: pointer;' : ''}">
          ${item.icon && item.iconPosition === "left"
            ? html`<img class="listItemIcon" src="${item.icon}" alt="Icon" />`
            : ""}
          <div class="listItemContent">
            ${item.caption
              ? html`<p class="listItemCaption">${item.caption}</p>`
              : ""}
            ${item.text
              ? html`<p class="listItemText">${item.text}</p>`
              : ""}
          </div>
          ${item.customContent
            ? html`<div class="listItemCustomContent">${item.customContent}</div>`
            : ""}
          ${item.icon && (!item.iconPosition || item.iconPosition === "right") && !item.customContent
            ? html`<img class="listItemIcon" src="${item.icon}" alt="Icon" />`
            : ""}
        </div>
        ${index < subItems.length - 1 ? html`<div class="divider full-width"></div>` : ""}
      `
    )}
  </div>
`;
