import { html } from "lit-html";
import "./healthgoalCard_inactive.css";
import { Tag } from "./tag.js";

export const healthgoalCard_inactive = ({
  cardImage,
  tagCategory,
  tagIcon,
  tagColor,
  tagTextColor,
  healthgoalTitle,
  healthgoalCoop = "",
  pillText = "",
  pillColor = "",
  goalinfoIcon1 = "",
  goalinfoText1 = "",
  goalinfoIcon2 = "",
  goalinfoText2 = "",
}) => html`
  <div class="healthgoal-card">
    <img src="${cardImage}" alt="Health goal image" class="card-image" />
    <div class="healthgoal-card-content">
      ${Tag(tagColor, tagIcon, tagCategory, tagTextColor)}
      <p class="healthgoal-title semibold tag-text">${healthgoalTitle}</p>
      ${healthgoalCoop
        ? html`<p class="healthgoal-coop caption tag-text">
            ${healthgoalCoop}
          </p>`
        : ""}
      ${pillText
        ? html`
            <div class="pill" style="background-color: var(${pillColor})">
              <p class="caption semibold">${pillText}</p>
            </div>
          `
        : ""}
      ${(goalinfoIcon1 && goalinfoText1) || (goalinfoIcon2 && goalinfoText2)
        ? html`
            <div class="goal-info">
              ${goalinfoIcon1 && goalinfoText1
                ? html`
                    <div class="info-item">
                      <img
                        src="${goalinfoIcon1}"
                        alt="Info icon 1"
                        class="info-icon"
                      />
                      <span class="info-text small tag-text"
                        >${goalinfoText1}</span
                      >
                    </div>
                  `
                : ""}
              ${goalinfoIcon2 && goalinfoText2
                ? html`
                    <div class="info-item">
                      <img
                        src="${goalinfoIcon2}"
                        alt="Info icon 2"
                        class="info-icon"
                      />
                      <span class="info-text small tag-text"
                        >${goalinfoText2}</span
                      >
                    </div>
                  `
                : ""}
            </div>
          `
        : ""}
    </div>
  </div>
`;
