import { html } from "lit-html";
import "./dialog.css";
import { router } from "../router.js";

/**
 * Dialog component
 *
 * @param {Object} props - Component properties
 * @param {string} props.title - Dialog title
 * @param {string} [props.text] - Optional dialog text
 * @param {Object} [props.buttonPrimary] - Optional primary button configuration
 * @param {string} props.buttonPrimary.text - Button text
 * @param {Function} [props.buttonPrimary.onClick] - Button click handler
 * @param {string} [props.buttonPrimary.link] - Optional navigation link
 * @param {Object} [props.buttonSecondary] - Optional secondary button configuration
 * @param {string} props.buttonSecondary.text - Button text
 * @param {Function} [props.buttonSecondary.onClick] - Button click handler
 * @param {string} [props.buttonSecondary.link] - Optional navigation link
 * @param {Object} [props.buttonTertiary] - Optional tertiary button configuration
 * @param {string} props.buttonTertiary.text - Button text
 * @param {Function} [props.buttonTertiary.onClick] - Button click handler
 * @param {string} [props.buttonTertiary.link] - Optional navigation link
 * @param {string} [props.icon] - Optional path to icon image
 * @param {string} [props.iconColor] - Optional background color for the icon bubble (default: --bubble-yellow)
 * @param {string} [props.iconCorner] - Optional corner style for the icon bubble ('round', 'top-left', 'top-right', 'bottom-left', 'bottom-right')
 * @param {string} [props.image] - Optional path to dialog image
 * @param {Function} [props.onClose] - Function to call when dialog should be closed
 * @returns {TemplateResult} The dialog template
 */
export const dialog = ({
  title,
  text,
  buttonPrimary,
  buttonSecondary,
  buttonTertiary,
  icon,
  iconColor,
  iconCorner = 'round',
  image,
  onClose
}) => {
  // Hilfsfunktion für Button-Klicks
  const handleButtonClick = (buttonConfig) => {
    // Benutzerdefinierten Handler ausführen (falls vorhanden)
    if (buttonConfig.onClick) {
      buttonConfig.onClick();
    }

    // Dialog schließen
    if (onClose) {
      onClose();
    }

    // Wenn ein Link angegeben ist, dorthin navigieren
    if (buttonConfig.link) {
      router.navigate(buttonConfig.link);
    }
  };

  // Bestimme die CSS-Klasse für die Icon-Bubble
  const getBubbleClass = (corner) => {
    const validCorners = ['round', 'top-left', 'top-right', 'bottom-left', 'bottom-right'];
    return validCorners.includes(corner) ? `bubble-${corner}` : 'bubble-round';
  };

  // Erstelle den Inline-Style für die Icon-Farbe
  const getIconStyle = (color) => {
    return color ? `background-color: ${color};` : '';
  };

  return html`
    <div class="dialog-container">
      ${icon ? html`
        <div class="dialog-icon-container ${getBubbleClass(iconCorner)}" style="${getIconStyle(iconColor)}">
          <img class="dialog-icon" src="${icon}" alt="Icon">
        </div>
      ` : ''}

      <div class="dialog-content">
        ${image ? html`<img class="dialog-image" src="${image}" alt="">` : ''}

        <div class="dialog-text-container">
          <h2 class="dialog-title ${icon ? 'bubble-space' : ''}">${title}</h2>
          ${text ? html`<div class="dialog-text">${text}</div>` : ''}
        </div>

        <div class="dialog-button-container">
          ${buttonPrimary ? html`
            <button
              class="button-standard primary"
              @click=${() => handleButtonClick(buttonPrimary)}
            >
              ${buttonPrimary.text}
            </button>
          ` : ''}

          ${buttonSecondary ? html`
            <button
              class="button-standard secondary"
              @click=${() => handleButtonClick(buttonSecondary)}
            >
              ${buttonSecondary.text}
            </button>
          ` : ''}

          ${buttonTertiary ? html`
            <button
              class="button-standard tertiary"
              @click=${() => handleButtonClick(buttonTertiary)}
            >
              ${buttonTertiary.text}
            </button>
          ` : ''}
        </div>
      </div>
    </div>
  `;
};
