/**
 * Page Headline Component Styles
 */

/* Container for the page headline */
.page-headline-container {
  width: 100%;
  padding: 24px;
  background-color: var(--primary-brand);
  color: white;
}

/* Headline text */
.page-headline-container h1 {
  margin: 0;
  padding: 0;
}

/* Headline with two colors */
.page-headline-container .headline-duo-color {
  display: flex;
  flex-direction: column;
}

/* First part of the headline */
.page-headline-container .headline-part1 {
  color: white;
}

/* Second part of the headline */
.page-headline-container .headline-part2 {
  color: var(--primary-light-grn);
}