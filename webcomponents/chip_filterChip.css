.chip-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
  justify-content: flex-start;
}

.chip {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  background-color: var(--chip-background);
  color: var(--chip-text);
  border-radius: 100px;
  min-height: 40px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.chip.active {
  background-color: var(--chip-active-background);
  color: var(--chip-active-text);
}

.chip-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}
