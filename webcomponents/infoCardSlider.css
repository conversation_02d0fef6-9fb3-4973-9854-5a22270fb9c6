/* Info Card */
.card.info-card-mi-d-c {
  align-items: center;
  gap: 8px;
  flex-direction: row;
  width: 100%;
  color: var(--primary-brand);
}

.card.info-card-mi-d-c p {
  margin: 0;
}

.card.info-card-mi-d-c p.semibold {
  margin: 8px 0;
}

.card.info-card-mi-d-c p.small {
  min-height: 24px;
  padding-top: 4px;
}

.card.info-card-mi-d-c,
.card.info-card-mi-d-c * {
  cursor: pointer;
}

.card.info-card-mi-d-c > div {
  flex-grow: 1;
  align-content: flex-end;
  display: flex;
  flex-direction: column;
}

.card .img-button {
  color: var(--primary-brand);
  width: 24px;
  height: 24px;
  align-self: flex-end;
}

.button-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.link-text {
  margin-right: 8px;
}

/* Pfeil Animation */
.card.info-card-mi-d-c .img-button {
  transition: transform 0.3s ease;
}

.card.info-card-mi-d-c:hover .img-button {
  transform: translateX(4px);
}
