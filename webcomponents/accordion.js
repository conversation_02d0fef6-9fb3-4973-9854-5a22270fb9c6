import { html } from "lit-html";
import "./accordion.css";

export const accordion = ({
  title,
  text,
  buttonLabel = null,
  buttonUrl = null,
  badge = null,
  open = false,
  onToggle = () => {},
}) => html`
  <div class="accordion">
    <div class="accordion-header" @click=${onToggle}>
      <span class="accordion-title">${title}</span>
      <div class="accordion-actions">
        ${badge !== null && badge !== undefined
          ? html`<span class="accordion-badge">${badge}</span>`
          : ""}
        <button
          class="accordion-toggle-btn"
          aria-label=${open ? "Schließen" : "Öffnen"}
          tabindex="-1"
        >
          ${open
            ? html`<span class="icon-minus">−</span>`
            : html`<span class="icon-plus">+</span>`}
        </button>
      </div>
    </div>
    ${open
      ? html`
          <div class="accordion-content">
            <div class="accordion-text">
              <p>${text}</p>
            </div>
            ${buttonLabel && buttonUrl
              ? html`
                  <a
                    class="button-standard secondary accordion-button"
                    href="${buttonUrl}"
                    data-navigo
                  >
                    ${buttonLabel}
                  </a>
                `
              : ""}
          </div>
        `
      : ""}
    <div class="accordion-divider"></div>
  </div>
`;
