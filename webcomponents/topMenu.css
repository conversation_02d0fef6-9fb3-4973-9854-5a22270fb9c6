/**
 * Top Menu Component Styles
 */

/* Container for the top menu */
.top-menu-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 24px;
  margin-top: -8px;
  width: 100%;
}

/* Icon containers */
.top-menu-container .icon-back,
.top-menu-container .icon-menu {
  display: flex;
  align-items: center;
}

.top-menu-container .icon-menu {
  display: flex;
  gap: 16px;
}

/* SVG icon styling */
.svgicon .svgpath {
  fill: var(--aok-grn-8);
}

/* Image styling */
.top-menu-container img {
  cursor: pointer;
}

.top-menu-container a {
  display: flex;
  align-items: center;
}
