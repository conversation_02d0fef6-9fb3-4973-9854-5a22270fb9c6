.feature-card-small {
  display: flex;
  width: 104px;
  min-width: 104px;
  flex-direction: column;
  align-items: center;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08),
    0px 2px 24px 0px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
}

.feature-card-small .image-container {
  background-color: var(--feature-card-background-blue);
  display: flex;
  height: 87px;
  max-width: 104px;
  padding: 16px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
}

.feature-card-small .content-container {
  color: var(--cards-text);
  display: flex;
  width: 104px;
  min-height: 65px;
  height: 100%;
  padding: 8px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.feature-card-small .content-container p {
  text-align: center;
}
