import { html } from "lit-html";
import "./progressBar.css";

export const progressBar = ({
  completedChallenges = 0,
  totalChallenges = 0,
  type = "healthgoal", // Standardwert: "healthgoal"
}) => {
  // Berechnung des Fortschritts in Prozent
  const progressPercentage =
    totalChallenges > 0
      ? Math.round((completedChallenges / totalChallenges) * 100)
      : 0;

  // Dynamischer Beschreibungstext basierend auf dem Typ
  const descriptionText =
    type === "poll"
      ? html`<p class="challenge-progress semibold green-text">
          Schritt ${completedChallenges} von ${totalChallenges}
        </p>`
      : html`<p class="challenge-progress caption tag-text">
          ${completedChallenges} von ${totalChallenges} Challenges geschafft
        </p>`;

  return html`
    <div class="progress-bar">
      <div class="progress" style="width: ${progressPercentage}%"></div>
    </div>
    ${descriptionText}
  `;
};
