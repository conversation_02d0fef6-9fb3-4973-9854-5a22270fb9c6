import { html } from "lit-html";
import "./healthgoalCard_inactive.css"; // Keep CSS filename for now to avoid breaking styles
import { Tag } from "./tag.js";

/**
 * Renders an inactive health goal card with various optional elements
 * 
 * @param {Object} props - Component properties
 * @param {string} props.cardImage - Path to the card image
 * @param {string} props.tagCategory - Category text for the tag
 * @param {string} props.tagIcon - Icon for the tag
 * @param {string} props.tagColor - Background color for the tag
 * @param {string} props.tagTextColor - Text color for the tag
 * @param {string} props.healthgoalTitle - Title of the health goal
 * @param {string} [props.healthgoalCoop=""] - Cooperation partner text
 * @param {string} [props.pillText=""] - Text for the pill element
 * @param {string} [props.pillColor=""] - Color for the pill element
 * @param {string} [props.goalinfoIcon1=""] - Icon for the first goal info
 * @param {string} [props.goalinfoText1=""] - Text for the first goal info
 * @param {string} [props.goalinfoIcon2=""] - Icon for the second goal info
 * @param {string} [props.goalinfoText2=""] - Text for the second goal info
 * @param {string} [props.link=""] - URL for navigation when card is clicked (uses data-navigo)
 * @returns {TemplateResult} The health goal card template
 */
export const healthgoalCardInactive = ({
  cardImage,
  tagCategory,
  tagIcon,
  tagColor,
  tagTextColor,
  healthgoalTitle,
  healthgoalCoop = "",
  pillText = "",
  pillColor = "",
  goalinfoIcon1 = "",
  goalinfoText1 = "",
  goalinfoIcon2 = "",
  goalinfoText2 = "",
  link = ""
}) => {
  // Card content template
  const cardContent = html`
    <div class="healthgoal-card" data-category="${tagCategory}">
      <img src="${cardImage}" alt="Health goal image" class="card-image" />
      <div class="healthgoal-card-content">
        ${Tag(tagColor, tagIcon, tagCategory, tagTextColor)}
        <p class="healthgoal-title semibold tag-text">${healthgoalTitle}</p>
        ${healthgoalCoop
          ? html`<p class="healthgoal-coop caption tag-text">
              ${healthgoalCoop}
            </p>`
          : ""}
        ${pillText
          ? html`
              <div class="pill" style="background-color: var(${pillColor})">
                <p class="caption semibold">${pillText}</p>
              </div>
            `
          : ""}
        ${(goalinfoIcon1 && goalinfoText1) || (goalinfoIcon2 && goalinfoText2)
          ? html`
              <div class="goal-info">
                ${goalinfoIcon1 && goalinfoText1
                  ? html`
                      <div class="info-item">
                        <img
                          src="${goalinfoIcon1}"
                          alt="Info icon 1"
                          class="info-icon"
                        />
                        <span class="info-text small tag-text"
                          >${goalinfoText1}</span
                        >
                      </div>
                    `
                  : ""}
                ${goalinfoIcon2 && goalinfoText2
                  ? html`
                      <div class="info-item">
                        <img
                          src="${goalinfoIcon2}"
                          alt="Info icon 2"
                          class="info-icon"
                        />
                        <span class="info-text small tag-text"
                          >${goalinfoText2}</span
                        >
                      </div>
                    `
                  : ""}
              </div>
            `
          : ""}
      </div>
    </div>
  `;

  // If link is provided, wrap the card in an anchor tag with data-navigo
  return link
    ? html`<a href="${link}" data-navigo>${cardContent}</a>`
    : cardContent;
};

// For backward compatibility
export const healthgoalCard_inactive = healthgoalCardInactive;
