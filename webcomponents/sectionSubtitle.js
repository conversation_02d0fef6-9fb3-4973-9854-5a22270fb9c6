import { html } from "lit-html";
import "./sectionSubtitle.css";

/**
 * Renders a section subtitle
 * 
 * @param {string} text - The subtitle text
 * @param {string} color - Optional color class (e.g. 'dark-grn-text')
 * @param {boolean} centered - Whether to center the text
 * @returns {TemplateResult} The section subtitle template
 */
export const sectionSubtitle = (text, color = '', centered = false) => {
  return html`
    <h3 class="section-subtitle semibold ${color} ${centered ? 'text-center' : ''}">${text}</h3>
  `;
};
