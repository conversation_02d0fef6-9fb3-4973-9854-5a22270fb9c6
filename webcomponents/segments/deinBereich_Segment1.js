import { html } from "lit-html";
import { sectionTitle } from "../sectionTitle.js";
import { infoCardwButton } from "../infoCardwButton.js";
import iconMountain from "../../svg/icons/icon_mountain.svg";
import iconHeartcheck from "../../svg/icons/icon_heartcheck.svg";

export const deinBereich_segment1Content = html`
  <div class="tabpadding">
    <div class="content-no-side-padding content-no-top-padding">
      ${sectionTitle("Keine anstehenden Termine")}
      <div class="content-left-align content-padding black-text">
        <p>
          Momentan steht nichts bei Dir an, ich habe Dir ein paar Vorschläge mitgebracht:
        </p>
      </div>
      <div class="content-padding content-bottom-padding">
        ${infoCardwButton({
          icoPath: iconMountain,
          text: "Du bist motiviert? Challenge Dich selbst mit passenden Gesundheitszielen.",
          linkText: "<PERSON><PERSON> den Zielen",
          link: "/healthgoals-overview"
        })}
      </div>
      <div class="content-padding">
        ${infoCardwButton({
          icoPath: iconHeartcheck,
          text: "Lege einen Vorsorgeplan an und behalte Deine Termine im Blick.",
          linkText: "Zum Vorsorgekompass"
        })}
      </div>
    </div>
  </div>
`;
