/* Spezifische Anpassung für die healthgoalsOverview-Seite */
.page-content[data-page="healthgoals-overview"],
.page-content[data-page="hg-fitUmgebung"],
.page-content[data-page="hg-niveau-poll"] {
  padding-top: 8px;
  margin: 0;
}

.page-content[data-page="healthgoals-overview"] .top-menu-container,
.page-content[data-page="hg-fitUmgebung"] .top-menu-container,
.page-content[data-page="hg-niveau-poll"] .top-menu-container {
  margin-top: 16px;
}

.tabheadline {
  margin-top: 0;
}

.empty-container {
  min-height: 4rem;
}
