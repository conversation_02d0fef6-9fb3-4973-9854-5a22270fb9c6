import { html } from "lit-html";
import { sectionTitle } from "../sectionTitle.js";
import { infoCardwButton } from "../infoCardwButton.js";
import iconDocteam from "../../svg/icons/icon_docteam.svg";
import iconPhonebubbles from "../../svg/icons/icon_phonebubbles.svg";

export const deinBereich_segment2Content = html`
  <div class="tabpadding">
    <div class="content-no-side-padding content-no-top-padding">
      ${sectionTitle("Gesundheitshistorie")}
      <div class="content-left-align content-padding black-text">
        <p>
          Du hast noch keine Dokumente über abgeschlossene Aktionen in der App.
        </p>
      </div>
      <div class="content-padding content-bottom-padding">
        ${infoCardwButton({
          icoPath: iconDocteam,
          text: "Per Videochat mit einem Arzt sprechen - egal wo Du Dich befindest.",
          linkText: "Zur Videosprechstunde"
        })}
      </div>
      <div class="content-padding">
        ${infoCardwButton({
          icoPath: iconPhonebubbles,
          text: "Du fühlst Dich krank? Erhalte mögliche Diagnosen und erste Empfehlungen per Chat.",
          linkText: "Zum Symptomcheck"
        })}
      </div>
    </div>
  </div>
`;
