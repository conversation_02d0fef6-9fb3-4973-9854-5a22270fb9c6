import { html } from "lit-html";
import { sectionTitle } from "../sectionTitle.js";
import { sectionParagraph } from "../sectionParagraph.js";
import { buttonTextIcon } from "../buttonTextIcon.js";
import { infoCardwButton } from "../infoCardwButton.js";
import iconArrowRight from "../../svg/icons/ArrowRight.svg";
import iconMoneyHand from "../../svg/icons/icon_moneyhand.svg";

export const healthgoalOverview_segment2Content = html`
  <div class="content-bottom-padding">
    <div class="standard-container">
      ${sectionTitle("Dein Wille zahlt sich aus")}
      ${sectionParagraph(
        "Deine Bonuspunkte, die Du hier bei AOK NAVIDA sammelst, übermitteln wir automatisch an Dein AOK Bonusprogramm. Dort kannst Du sie Dir auszahlen lassen. <PERSON>te beachte, dass Du pro Jahr für maximal vier abgeschlossene Gesundheitsziele Punkte erhältst."
      )}
    </div>
    <div class="standard-container content-padding content-bottom-padding">
      ${buttonTextIcon("Mehr zu Bonuspunkten", iconArrowRight)}
    </div>
    <div
      class="standard-container content-padding content-bottom-small-padding"
    >
      ${infoCardwButton({
        icoPath: iconMoneyHand,
        title: "",
        text: "Du bist noch nicht im Bonusprogramm? Worauf wartest Du noch? Hier kannst Du Dich registrieren:",
        linkText: "Zum Bonusprogramm",
        background: "--info-card-background-yellow",
      })}
    </div>
    <div class="standard-container content-padding">
      ${infoCardwButton({
        title: "Wichtig!",
        text: "Bitte denke daran, Dich vor Abschluss Deines ersten Gesundheitszieles in unser Bonusprogramm einzuschreiben. So kannst Du sicher sein, dass Deine Punkte übertragen werden und nicht verfallen.",
        linkText: "",
        background: "--info-card-background-green",
      })}
    </div>
  </div>
`;
