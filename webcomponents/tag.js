import { html } from "lit-html";
import "./tag.css";

export const Tag = (tagColor, tagIcon, tagCategory, tagTextColor) => html`
  <div class="tag" style="background-color: var(${tagColor})">
    ${tagIcon 
      ? html`<img src="${tagIcon}" alt="${tagCategory} icon" class="tag-icon" />` 
      : ""
    }
    <span class="tag-text caption" style="color: var(${tagTextColor})"
      >${tagCategory}</span
    >
  </div>
`;

