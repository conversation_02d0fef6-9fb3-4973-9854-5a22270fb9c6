.healthgoal-cards {
  width: 100%;
}

.healthgoal-cards-container {
  width: 100%;
}

/* Animation für das Ein- und Ausblenden der Karten */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(10px);
  }
}

.healthgoal-card {
  display: flex;
  background-color: white;
  margin: 24px 0 0 0;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08),
    0px 2px 24px 0px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Klassen für die Animation */
.healthgoal-card.fade-in {
  animation: fadeIn 0.3s ease forwards;
}

.healthgoal-card.fade-out {
  animation: fadeOut 0.3s ease forwards;
}

.card-image {
  width: 88px;
  flex-shrink: 0;
  align-self: stretch;
  object-fit: cover; /* <PERSON><PERSON> stellt sicher, dass das Bild den verfügbaren Platz ausfüllt */
}

.healthgoal-card-content {
  flex-grow: 1;
  padding: 16px;
  background-color: white;
}

.healthgoal-card-content > p {
  margin: 16px 0;
}

.healthgoal-title {
  /* Styling für den Titel */
}

.healthgoal-coop {
  /* Styling für den Kooperationstext */
}

.pill {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 16px;
  margin: 0 0 16px 0;
}

.pill > p {
  margin: 0;
}

.goal-info {
  display: flex;
  flex-direction: column;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 4px 0;
}

.info-icon {
  /* Styling für die Info-Icons */
}

.info-text {
  /* Styling für den Info-Text */
}
