/* Info Card Plain */
.card.info-card-plain {
  display: flex;
  align-items: center; /* Zentriert Icon und Text vertikal */
  gap: 16px; /* Abstand zwischen Icon und Text */
  width: 100%;
  padding: 16px; /* Innenabstand der Karte */
  border-radius: 8px; /* Abgerundete Ecken */
  flex-direction: row;
}

.card.info-card-plain p {
  margin: 0; /* Entfernt Standard-Abstände */
  font-size: 0.875rem; /* 14px in rem */
}

.card.info-card-plain .icon-container {
  flex-shrink: 0; /* <PERSON><PERSON><PERSON><PERSON><PERSON>, dass das Icon schrumpft */
  display: flex;
}

.card.info-card-plain .icon-container img {
  width: 40px; /* Größe des Icons */
  height: auto;
}

.card.info-card-plain .content-container {
  flex-grow: 1; /* Lässt den Text den verfügbaren Platz nutzen */
  display: flex;
  flex-direction: column;
}

.card.info-card-plain .title {
  margin-bottom: 8px;
  margin-top: 4px;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.35rem;
}

.card.info-card-plain .caption {
  font-weight: normal;
  font-size: 1rem;
  line-height: 1.35rem;
}
