/* Dialog Component Styles */

/* Dialog Overlay */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  border-radius: 48px;
}

/* Dialog Container Wrapper */
.dialog-container-wrapper {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

/* Dialog Container */
.dialog-container {
  display: flex;
  width: 327px;
  padding: 24px;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  border-radius: 12px;
  background: var(--dialog-background);
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 2px 24px 0px rgba(0, 0, 0, 0.08);
  position: relative;
}

/* Dialog Inhalt */
.dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  align-self: stretch;
}

/* Dialog Text Container */
.dialog-text-container {
  display: flex;
  font-family: "AOK Buenos Aires Text";
  flex-direction: column;
  align-items: center;
  gap: 16px;
  align-self: stretch;
}

/* Dialog Titel */
.dialog-title {
  align-self: stretch;
  color: var(--dialog-text);
  font-family: "AOK Buenos Aires Text";
  font-size: 1.25rem;
  font-style: normal;
  font-weight: 600;
  line-height: 1.35rem;
  letter-spacing: 0.25px;
  text-align: left;
  margin: 0;
}

/* Zusätzlicher Abstand für den Titel, wenn eine Bubble vorhanden ist */
.dialog-title.bubble-space {
  margin-top: 48px;
}

/* Dialog Text */
.dialog-text {
  align-self: stretch;
  color: var(--dialog-text);
  font-family: "AOK Buenos Aires Text";
  font-size: 1rem;
  font-style: normal;
  font-weight: 400;
  line-height: 1.35rem;
  letter-spacing: 0.15px;
  text-align: left;
  margin: 0;
}

.dialog-button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  align-self: stretch;
}

/* Spezifische Styles für den Secondary Button im Dialog */
.dialog-button-container .button-standard.secondary {
  background: transparent;
  margin-top: 0;
}

.dialog-button-container .button-standard.secondary:hover,
.dialog-button-container .button-standard.secondary:active,
.dialog-button-container .button-standard.secondary:focus {
  border-radius: 4px;
  border: 2px solid var(--button-secondary-on-backgound-pressed, #005E3F);
  background: var(--button-secondary-background-pressed, #E0ECE8);
}

/* Icon Container Basis-Stil */
.dialog-icon-container {
  width: 80px;
  height: 80px;
  position: absolute;
  right: 123px;
  top: -40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--bubble-yellow);
  z-index: 1;
}

/* Runde Bubble (Standard) */
.dialog-icon-container.bubble-round {
  border-radius: 50%;
}

/* Bubble mit spitzer Ecke oben links */
.dialog-icon-container.bubble-top-left {
  border-radius: 0 50% 50% 50%;
}

/* Bubble mit spitzer Ecke oben rechts */
.dialog-icon-container.bubble-top-right {
  border-radius: 50% 0 50% 50%;
}

/* Bubble mit spitzer Ecke unten links */
.dialog-icon-container.bubble-bottom-left {
  border-radius: 50% 50% 50% 0;
}

/* Bubble mit spitzer Ecke unten rechts */
.dialog-icon-container.bubble-bottom-right {
  border-radius: 50% 50% 0 50%;
}

.dialog-icon {
  width: 48px;
  height: 48px;
  flex-shrink: 0;
}

.dialog-image {
  width: 160px;
  height: 160px;
  object-fit: contain;
}
