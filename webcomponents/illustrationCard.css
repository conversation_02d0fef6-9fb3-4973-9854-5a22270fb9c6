.illustration-card {
  color: var(--cards-text);
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  border-radius: 8px;
  width: 100%;
}

.illustration-card .content-container {
  width: 100%;
}

.illustration-card .content-container > * {
  margin: 12px 0;
}

.illustration-card button {
  color: var(--text-button-text);
  background: transparent;
  border: none;
  padding: 0;
  transition: opacity 0.3s ease-in-out;
  justify-content: flex-start;
}

.illustration-card button:hover {
  cursor: pointer;
  color: var(--text-button-text);
  background: transparent;
  border: none;
  opacity: 0.8;
}

.illustration-card button > img {
  padding-left: 8px;
  transition: padding 0.3s ease-in-out;
}

.illustration-card button:hover > img {
  padding-left: 12px;
  transition: padding 0.3s ease-in-out;
}

.illustration-card a {
  color: var(--text-button-text);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: opacity 0.3s ease-in-out;
  font-family: "AOK Buenos Aires Text";
  font-weight: 600;
  font-size: 1rem;
  letter-spacing: 0;
  line-height: 1.35rem;
  margin-bottom: 0 !important;
}

.illustration-card a:hover {
  cursor: pointer;
}
