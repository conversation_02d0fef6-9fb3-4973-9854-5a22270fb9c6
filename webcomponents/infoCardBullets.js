import { html } from "lit-html";
import "./infoCardBullets.css";
import iconCheck from "../svg/icons/icon_check.svg";

export const infoCardBullets = ({
  icoPath,
  title,
  text,
  bulletPoints = [],
}) => html`
  <div class="card info-card">
    <div class="icon-container">
      <!-- Icon anzeigen, falls icoPath definiert ist -->
      ${icoPath
        ? html`
            <div class="icon-container">
              <img src="${icoPath}" alt="Icon" />
            </div>
          `
        : ""}
    </div>
    <div class="content-container">
      <div class="text-content">
        ${title ? html`<p class="title semibold green-text">${title}</p>` : ""}
        ${text ? html`<p class="description small">${text}</p>` : ""}
      </div>
      <ul class="bullet-points">
        <!-- Bullet points are rendered as map array to be able to insert multiple points via , -->
        ${bulletPoints.map(
          (point) => html`
            <li class="bullet-point">
              <img src="${iconCheck}" alt="Check Icon" class="check-icon" />
              <p class="bullet-text small">${point}</p>
            </li>
          `
        )}
      </ul>
    </div>
  </div>
`;
