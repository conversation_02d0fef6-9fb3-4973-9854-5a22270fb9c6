import { html } from "lit-html";
import "./infoCardwButton.css";
import iconArrowRight from "../svg/icons/ArrowRight.svg";

export const infoCardwButton = ({
  icoPath,
  title,
  text,
  caption,
  linkText,
  background = "--info-card-background-blue",
  link = null,
  linkIcon = null, // Neuer Parameter für eigenes Link-Icon
  linkTextAlign = "right", // Neuer Parameter für Textausrichtung ("left" oder "right")
}) => {
  // Bestimme das zu verwendende Icon
  const buttonIcon = linkIcon || iconArrowRight;
  const isDefaultIcon = !linkIcon; // true, wenn Standard-Icon verwendet wird


  // Card content template
  const cardContent = html`
    <div
      class="card info-card-w-button"
      style="background-color: var(${background});"
    >
      <div class="icon-container">
        ${icoPath ? html`<img src="${icoPath}" alt="Icon" />` : html``}
      </div>
      <div class="content-container">
        <div class="text-content">
          ${title ? html`<p class="title semibold">${title}</p>` : ""}
          ${text ? html`<p class="description small">${text}</p>` : ""}
          ${caption
            ? html`<p class="caption semibold"><strong>${caption}</strong></p>`
            : ""}
        </div>
        <div class="button-container ${linkTextAlign === "left" ? "align-left" : ""}">
          ${linkText
            ? html`
              <span class="link-text">${linkText}</span>
              <img
                class="img-button${isDefaultIcon ? ' is-animated' : ''}"
                src="${buttonIcon}"
                alt="Icon"
              />
              `
            : html``}
          <!-- Wenn linkText leer, dann kein Icon rendern -->
        </div>
      </div>
    </div>
  `;

  // If link is provided, wrap the card in an anchor tag with data-navigate
  return link
    ? html`<a href="${link}" data-navigate="${link}">${cardContent}</a>`
    : cardContent;
};
