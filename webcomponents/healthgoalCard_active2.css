.healthgoal-card-active {
  display: flex;
  flex-direction: column;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.card-image-active {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.active-pill {
  color: var(--tag-text);
  position: absolute;
  left: 8px;
  top: 8px;
}

.goal-info-active {
  display: flex;
  gap: 16px;
  width: 100%;
}

.goal-info-active .info-item {
  display: flex;
  align-items: center;
  box-sizing: border-box; /* Wichtig für präzise Berechnung */
  padding: 0; /* Sicherstellen dass kein Padding existiert */
  margin: 0; /* <PERSON><PERSON> zurücksetzen */
  gap: 8px;
}

.goal-info-active .info-icon {
  width: 16px;
  height: auto;
  flex-shrink: 0;
}

.goal-info-active .info-text {
  flex: 1;
  min-width: 0; /* Überlaufvermeidung */
  white-space: nowrap;
}

.healthgoal-card-active.elevated {
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.08), 0px 2px 24px rgba(0, 0, 0, 0.08);
}
