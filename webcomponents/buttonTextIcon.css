.text-icon-button {
  color: var(--text-button-text);
  background: transparent;
  border: none;
  padding: 0;
  transition: opacity 0.3s ease-in-out;
  justify-content: flex-start;
  margin-top: 0;
  display: flex; /* Ermöglicht flexibles Layout für Text und Icon */
  align-items: center; /* Zentriert Text und Icon vertikal */
  gap: 8px; /* Abstand zwischen Text und Icon */
}

.text-icon-button:hover {
  cursor: pointer;
  color: var(--text-button-text);
  background: transparent;
  border: none;
  opacity: 0.8;
}

.text-icon-button > img {
  transition: padding 0.3s ease-in-out;
}

.text-icon-button:hover > img {
  transition: padding 0.3s ease-in-out;
}

/* Icon rechts vom Text */
.icon-right {
  order: 1; /* Positioniert das Icon rechts vom Text */
}

/* Icon links vom Text */
.icon-left {
  order: -1; /* Positioniert das Icon links vom Text */
}

/* Optional: Zusätzliche Animation für das Icon beim Hover */
.text-icon-button:hover .icon-left {
  padding-right: 0px; /* keine Animation für Icon links */
}

.text-icon-button:hover .icon-right {
  padding-left: 4px; /* Animation für Icon rechts */
}