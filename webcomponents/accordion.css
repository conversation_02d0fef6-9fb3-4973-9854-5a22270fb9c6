.accordion {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0 24px;
}

.accordion-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  cursor: pointer;
  padding: 0;
  margin: 16px 0;
}

.accordion-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-text, #222);
  flex: 1 1 auto;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.accordion-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.accordion-badge {
  background: var(--aok-grn-8, #005e3f);
  color: var(--aok-grn-1, #fff);
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
}

.accordion-toggle-btn {
  background: none;
  border: none;
  font-size: 2rem;
  line-height: 1;
  cursor: pointer;
  color: var(--aok-grn-8, #005e3f);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
}

.icon-plus,
.icon-minus {
  font-size: 2rem;
  font-weight: 300;
  user-select: none;
}

.accordion-divider {
  height: 1px;
  background: var(--gray-2, #e0e0e0);
  width: 100%;
  margin: 0;
}

.accordion-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  padding: 0;
}

.accordion-text {
  color: var(--primary-text, #222);
  font-size: 1rem;
  line-height: 1.5;
}

.accordion-text p {
  margin: 0;
  margin-bottom: 16px;
}

.button-standard.accordion-button {
  font-size: 0.9rem;
  cursor: pointer;
}

.accordion-toggle-btn:hover,
.accordion-toggle-btn:active {
  background-color: inherit !important;
  background: inherit !important;
}
