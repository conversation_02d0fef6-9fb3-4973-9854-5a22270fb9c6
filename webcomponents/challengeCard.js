import { html } from "lit-html";
import "./challengeCard.css";

/**
 * Renders a challenge card with image and info items
 *
 * @param {Object} props - Component properties
 * @param {string} props.imgPath - Path to the card image
 * @param {string} props.title - Card title
 * @param {Object[]} [props.infoItems=[]] - Array of info items
 * @param {string} props.infoItems[].icoPath - Path to the info item icon
 * @param {string} props.infoItems[].text - Info item text
 * @param {string} [props.link=""] - URL for navigation when card is clicked (uses data-navigo)
 * @param {Object} [props.statusPill] - Optional status pill configuration
 * @param {string} props.statusPill.text - Text to display in the pill
 * @param {string} props.statusPill.type - Type of pill: 'active' or 'done'
 * @returns {TemplateResult} The challenge card template
 */
export const challengeCard = ({
  imgPath,
  title,
  infoItems = [],
  link = "",
  statusPill = null
}) => {
  // Card content template
  const cardContent = html`
    <div class="challenge-card">
      <div class="challenge-card-image">
        <img src="${imgPath}" alt="Challenge image" />
      </div>
      <div class="challenge-card-content">
        <p class="challenge-card-title semibold">${title}</p>
        ${infoItems.map(item => html`
          <div class="challenge-card-info-item">
            <img src="${item.icoPath}" alt="Info icon" class="challenge-card-info-icon" />
            <p class="challenge-card-info-text">${item.text}</p>
          </div>
        `)}
        ${statusPill ? html`
          <div class="pill ${statusPill.type === 'done' ? 'done-pill' : 'active-pill'}">
            <p class="caption semibold">${statusPill.text}</p>
          </div>
        ` : ''}
      </div>
    </div>
  `;

  // If link is provided, wrap the card in an anchor tag with data-navigo
  return link
    ? html`<a href="${link}" data-navigo>${cardContent}</a>`
    : cardContent;
};
