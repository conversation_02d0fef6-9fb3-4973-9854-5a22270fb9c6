/* Container für horizontales Scrollen mehrerer Karten */
.image-card-narrow-scroll-container {
  max-width: var(--phone-screen-width);
  padding-left: 24px;
  overflow-x: hidden; /* <PERSON>er<PERSON><PERSON><PERSON> doppeltes Scrollen */
  position: relative; /* Für korrekte Positionierung des inneren Containers */
}

.image-card-narrow-scroll-container::-webkit-scrollbar {
  display: none;
}

.image-card-narrow-container, .image-card-narrow-container.standard-container {
  display: flex;
  flex-direction: row; 
  flex-wrap: nowrap;
  gap: 16px;
  overflow-x: auto; /* Erlaubt horizontales Scrollen */
  padding: 8px 24px 8px 0;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scroll-snap-type: x proximity; /* Ändere von 'mandatory' zu 'proximity' für sanfteres Snapping */
  scroll-behavior: smooth; /* Fügt sanfte Übergänge hinzu */
  width: 100%;
  -webkit-overflow-scrolling: touch;
  cursor: grab; /* <PERSON><PERSON><PERSON> an, dass der Inhalt ziehbar ist */
}

/* <PERSON><PERSON> den Cursor ändern */
.image-card-narrow-container:active {
  cursor: grabbing;
}

.image-card-narrow-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Karten-Styling */
.image-card-narrow {
  flex: 0 0 auto;
  width: calc(50% - 8px);
  min-width: 155px;
  max-width: 155px;
  scroll-snap-align: start;
  transition: transform 0.2s ease; /* Sanfte Transformation beim Hover */
}

/* Hover-Effekt für Karten */
.image-card-narrow:hover {
  transform: scale(0.95); /* Leichtes Verkleinern beim Hover */
}

.image-card-narrow-img {
  width: 100%;
  height: 230px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 2px 24px 0px rgba(0, 0, 0, 0.08);
}

.image-card-narrow-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-card-narrow-title {
  align-self: stretch;
  color: var(--text-body-default, #293033);
  font-family: "AOK Buenos Aires Text";
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 21.6px;
  letter-spacing: 0.15px;
  margin-top: 8px;
  margin-bottom: 0;
}
