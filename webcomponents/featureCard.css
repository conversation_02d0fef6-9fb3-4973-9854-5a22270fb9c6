.feature-card {
  display: flex;
  width: 100%;
  min-width: 327px;
  flex-direction: row;
  align-items: center;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08),
    0px 2px 24px 0px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
}

.feature-card .image-container {
  background-color: var(--feature-card-background-green);
  display: flex;
  max-width: 88px;
  padding: 16px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
}

.feature-card .content-container {
  color: var(--cards-text);
  display: flex;
  width: 100%;
  min-height: 83px;
  height: 100%;
  padding: 16px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
}

.feature-card .content-container p {
  text-align: left;
  margin: 0;
  width: 100%;
}

a {
  text-decoration: none;
  cursor: pointer;
}
