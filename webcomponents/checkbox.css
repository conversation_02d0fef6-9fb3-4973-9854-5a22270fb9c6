.custom-checkbox {
  display: flex;
  align-items: center; /* Zentriert Checkbox und Text vertikal */
}

.checkbox {
  display: none; /* Versteckt die Standard-Checkbox */
}

/* Custom Checkbox Style */
.checkbox-label {
  display: flex;
  align-items: baseline; /* Zentriert das Icon und die erste Zeile Text vertikal */
  position: relative;
  cursor: pointer;
}

.checkbox-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0; /* <PERSON>er<PERSON><PERSON><PERSON>, dass das Icon schrumpft */
  border-radius: 2px;
  border: 2px solid var(--checkboxes-deselected-background);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent; /* Standard-Hintergrund */
  transition: background-color 0.3s ease-in-out, border-color 0.3s ease-in-out;
}

.checkbox-text {
  align-self: stretch; /* Ermöglicht mehrzeiligen Text */
  margin-left: 16px; /* Abstand zwischen Checkbox und Text */
  color: var(--list-item-text);
  font-family: "AOK Buenos Aires Text";
  font-size: 1rem;
  font-style: normal;
  font-weight: 600;
  line-height: 1.35rem; /* Abstand zwischen den Zeilen (135%) */
  letter-spacing: 0.15px;
}

/* Checked State */
.checkbox:checked + .checkbox-label .checkbox-icon {
  background-color: var(--primary-brand); /* Grün bei Aktivierung */
}

.checkbox:checked + .checkbox-label .checkbox-icon img {
  display: block; /* Zeigt das Haken-Icon an */
  width: 16px;
  height: 16px;
}

/* Default State (Haken verstecken) */
.checkbox-icon img {
  display: none; /* Haken standardmäßig verstecken */
}
