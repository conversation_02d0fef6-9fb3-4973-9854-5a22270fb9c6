import { html } from "lit-html";
import "./infoCardPlain.css";

/**
 * Renders a plain information card with optional icon, title, text and caption
 *
 * @param {Object} props - Component properties
 * @param {string|null} [props.icon=null] - Path to the icon image
 * @param {string|null} [props.title=null] - Card title
 * @param {string|null} [props.text=null] - Main card text
 * @param {string|null} [props.caption=null] - Caption text
 * @param {string} [props.textColor="var(--caption-green)"] - Text color
 * @param {string} [props.backgroundColor="var(--info-card-background-blue)"] - Background color
 * @returns {TemplateResult} The info card template
 */
export const infoCardPlain = ({
  icon = null,
  title = null,
  text = null,
  caption = null,
  textColor = "var(--caption-green)",
  backgroundColor = "var(--info-card-background-blue)",
}) => html`
  <div
    class="card info-card-plain"
    style="background-color: ${backgroundColor}; color: ${textColor};"
  >
    ${icon
      ? html`
          <div class="icon-container">
            <img src="${icon}" alt="Icon" class="icon" />
          </div>
        `
      : ""}
    <div class="content-container ${!icon ? "full-width" : ""}">
      ${title ? html`<h3 class="title">${title}</h3>` : ""}
      ${text ? html`<p class="text">${text}</p>` : ""}
      ${caption ? html`<p class="caption">${caption}</p>` : ""}
    </div>
  </div>
`;
