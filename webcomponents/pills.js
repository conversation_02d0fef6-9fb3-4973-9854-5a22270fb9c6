import { html } from "lit-html";
import "./pills.css";

/**
 * Erstellt eine Pill-Komponente mit optionalem Icon
 * 
 * @param {Object} props - Komponenteneigenschaften
 * @param {string} props.text - Text der Pill
 * @param {string} [props.color="--light-green"] - Hintergrundfarbe der Pill
 * @param {string} [props.textColor="--dark-green"] - Textfarbe der Pill
 * @param {string} [props.iconName=null] - Name/Pfad des Icons
 * @param {string} [props.iconPosition="left"] - Position des Icons ("left" oder "right")
 * @returns {TemplateResult} Die Pill-Komponente
 */
export const pill = ({
  text,
  color = "--light-green",
  textColor = "--dark-green",
  iconName = null,
  iconPosition = "left"
}) => {
  return html`
    <div class="pill" style="background-color: var(${color}); color: var(${textColor})">
      ${iconName && iconPosition === "left" ? html`<img class="pill-icon" src="${iconName}" alt="Icon" />` : ''}
      <p class="caption semibold">${text}</p>
      ${iconName && iconPosition === "right" ? html`<img class="pill-icon" src="${iconName}" alt="Icon" />` : ''}
    </div>
  `;
};

/**
 * Erstellt einen Container mit mehreren Pills
 * 
 * @param {Array} pills - Array von Pill-Konfigurationen
 * @returns {TemplateResult} Der Pills-Container
 */
export const pillsContainer = (pills) => {
  return html`
    <div class="pills-container">
      ${pills.map(pillConfig => pill(pillConfig))}
    </div>
  `;
};
