import { html } from "lit-html";
import "./progressGaugeCard.css";

export const progressGaugeCard = ({
  title,
  description,
  completedTrainings,
  totalTrainings,
  backgroundColor = "--cards-progress-gauge-card-background-success",
  textColor = "--cards-progress-gauge-card-text",
  gaugeColor = "#00B67A"
}) => {
  const progressPercentage = totalTrainings > 0
    ? Math.round((completedTrainings / totalTrainings) * 100)
    : 0;

  // KORRIGIERTE Parameter - Zentrum nach unten verschoben
  const centerX = 66;
  const centerY = 130;  // DEUTLICH nach unten verschoben (war 69)
  const radius = 50;   // Kleinerer Radius für bessere Proportionen
  
  // 270° Bogen: von links unten (225°) nach rechts unten (315°)
  const startAngle = 225;
  const endAngle = 315;
  
  // Umrechnung in Radiant
  const startRad = (startAngle * Math.PI) / 180;
  const endRad = (endAngle * Math.PI) / 180;
  
  // Start- und Endpunkte berechnen
  const startX = centerX + Math.cos(startRad) * radius;
  const startY = centerY + Math.sin(startRad) * radius;
  const endX = centerX + Math.cos(endRad) * radius;
  const endY = centerY + Math.sin(endRad) * radius;
  
  // Bogenlänge für 270°
  const arcLength = (270 / 360) * 2 * Math.PI * radius;
  const strokeDashoffset = arcLength * (1 - progressPercentage / 100);

  return html`
    <div class="progressGaugeCard" style="background: var(${backgroundColor})">
      <div class="progressGaugeContainer">
        <div class="gaugeContainer">
          <svg width="132" height="105" viewBox="0 0 132 105" fill="none" xmlns="http://www.w3.org/2000/svg">
            
            <!-- Weißer Hintergrund-Bogen -->
            <g filter="url(#filter0_d_gauge)">
              <path 
                d="M ${startX} ${startY} A ${radius} ${radius} 0 1 1 ${endX} ${endY}"
                fill="none"
                stroke="white"
                stroke-width="12"
                stroke-linecap="round"
              />
            </g>

            <!-- Grüner Progress-Bogen -->
            <path 
              d="M ${startX} ${startY} A ${radius} ${radius} 0 1 1 ${endX} ${endY}"
              fill="none"
              stroke="${gaugeColor}"
              stroke-width="12"
              stroke-linecap="round"
              stroke-dasharray="${arcLength}"
              stroke-dashoffset="${strokeDashoffset}"
              style="transition: stroke-dashoffset 0.5s ease-in-out;"
            />

            <!-- Filter for drop shadow -->
            <defs>
              <filter id="filter0_d_gauge" x="0" y="0" width="132" height="104.88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                <feOffset dy="3"/>
                <feGaussianBlur stdDeviation="3"/>
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7428_31419"/>
                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7428_31419" result="shape"/>
              </filter>
            </defs>
          </svg>

          <!-- Percentage text - Position angepasst -->
          <p class="percentage-text" style="color: var(${textColor})">
            ${progressPercentage} %
          </p>

          <p class="gaugeText" style="color: var(${textColor}); text-align: center; margin: 0; margin-top: 8px;">
            ${completedTrainings} von ${totalTrainings}<br>erledigt
          </p>
        </div>
      </div>
      <div class="contentGaugeContainer">
        <h3 class="gaugeTitle" style="color: var(${textColor})">${title}</h3>
        <p class="gaugeText" style="color: var(${textColor})">${description}</p>
      </div>
    </div>
  `;
};
