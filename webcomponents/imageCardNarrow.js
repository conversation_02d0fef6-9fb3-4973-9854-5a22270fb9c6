import { html } from "lit-html";
import "./imageCardNarrow.css";

/**
 * Renders a narrow image card with title
 * 
 * @param {Object} props - Component properties
 * @param {string} props.imgPath - Path to the image
 * @param {string} props.title - Title text displayed below the image
 * @param {string} [props.altText=""] - Alternative text for the image
 * @param {string} [props.link=""] - Optional link URL (uses data-navigo)
 * @returns {TemplateResult} The narrow image card template
 */
export const imageCardNarrow = ({
  imgPath,
  title,
  altText = "",
  link = ""
}) => {
  // Card content template
  const cardContent = html`
    <div class="image-card-narrow">
      <div class="image-card-narrow-img">
        <img src="${imgPath}" alt="${altText || title}" />
      </div>
      <p class="image-card-narrow-title semibold">${title}</p>
    </div>
  `;

  // If link is provided, wrap the card in an anchor tag with data-navigo
  return link
    ? html`<a href="${link}" data-navigo>${cardContent}</a>`
    : cardContent;
};