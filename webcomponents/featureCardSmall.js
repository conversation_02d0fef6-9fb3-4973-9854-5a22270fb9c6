import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import "./featureCardSmall.css";

export const featureCardSmall = ({ imgPath, title }) => html`
  <div class="feature-card-small" style="background-color: #ffffff">
    <div class="image-container">
      <div class="image">
        <img src="${imgPath}" alt="Illustration" />
      </div>
    </div>
    <div class="content-container">
      <!-- Use Unsafe HTML to be able to render &shy; soft hyphens -->
      <p class="small">${unsafeHTML(title) || "Titel"}</p>
    </div>
  </div>
`;
