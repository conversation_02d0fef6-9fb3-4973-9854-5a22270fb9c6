import { html } from "lit-html";
import "./segmentedControl.css";

export function createSegmentedControl(segments) {
  const segmentedControlTemplate = html`
    <div class="tab">
      ${segments.map(
        (segment) => html`
          <button class="tablinks" @click=${(e) => openSegment(e, segment.id)}>
            ${segment.title}
          </button>
        `
      )}
    </div>
    ${segments.map(
      (segment) => html`
        <div id="${segment.id}" class="tabcontent">${segment.content}</div>
      `
    )}
  `;

  function openSegment(evt, segmentId) {
    const tabcontent = document.getElementsByClassName("tabcontent");
    for (let i = 0; i < tabcontent.length; i++) {
      tabcontent[i].style.display = "none";
    }
    const tablinks = document.getElementsByClassName("tablinks");
    for (let i = 0; i < tablinks.length; i++) {
      tablinks[i].className = tablinks[i].className.replace(" active", "");
    }
    document.getElementById(segmentId).style.display = "flex";
    evt.currentTarget.className += " active";
  }

  return {
    template: segmentedControlTemplate,
    initialize: () => {
      console.log("Initializing segmented control");
      const firstTab = document.querySelector(".tablinks");
      if (firstTab) {
        console.log("First Tab found, clicking");
        firstTab.click();
        firstTab.classList.add("active");
      } else {
        console.log("No .tablinks element found");
      }
    },
  };
}

console.log("Segmented control module loaded");
