import Navigo from "navigo";
import { app, appStorage } from "./utils.js";
import { render } from "lit-html";

// Importiere alle Templates statisch
import { templateAppstart01 } from "./src/welcome.js";
import { featuresTemplate } from "./src/features.js";
import {
  healthgoalsOverviewWithActiveGoalTemplate,
  healthgoalsOverviewNoGoalTemplate
} from "./src/healthgoalsOverview.js";
import { homescreenTemplate } from "./src/homescreen.js";
import { hg_fitUmgebungTemplate, initializeHealthGoalResetMenu } from "./src/healthgoals/hg_fitUmgebung.js";
import { segmentedControl } from "./src/healthgoalsOverview.js";
import { templateConsent } from "./src/healthgoals/hg_consent.js";
import { templateNiveauInitial } from "./src/healthgoals/hg_niveau_initial.js";
import { templateNiveauPoll } from "./src/healthgoals/hg_niveau_poll.js";
import { templateLockereWanderung, initializeLockereWanderungResetMenu } from "./src/healthgoals/challenges/ch_lockereWanderung.js";
import { templateGassiGehen, initializeGassiGehenResetMenu } from "./src/healthgoals/challenges/ch_gassiGehen.js";
import { templateFahrradTour, initializeFahrradTourResetMenu } from "./src/healthgoals/challenges/ch_fahrradTour.js";
import { templateSpazierenGehen, initializeSpazierenGehenResetMenu } from "./src/healthgoals/challenges/ch_spazierenGehen.js";
import { templatePlogging, initializePloggingResetMenu } from "./src/healthgoals/challenges/ch_plogging.js";
import { templateTreppensteigen } from "./src/healthgoals/trainings/tr_treppensteigen.js";
import { templateLockereWanderung as templateTrainingLockereWanderung } from "./src/healthgoals/trainings/tr_lockereWanderung.js";
import { templateGassigehen } from "./src/healthgoals/trainings/tr_gassigehen.js";
import { templateFahrradTour as templateTrainingFahrradTour } from "./src/healthgoals/trainings/tr_fahrradTour.js";
import { templateSpazierenGehen as templateTrainingSpazierenGehen } from "./src/healthgoals/trainings/tr_spazierenGehen.js";
import { templatePlogging as templateTrainingPlogging } from "./src/healthgoals/trainings/tr_plogging.js";
import { trackerConnectTemplate } from "./src/trackerConnect.js";
import { komootLoginTemplate } from "./src/healthgoals/komoot/komootLogin.js";
import { deinBereichTemplate, initializeDeinBereich } from "./src/deinBereich.js";
import { settingsTemplate } from "./src/settings.js";

/**
 * Verwaltet die Sichtbarkeit des Bottom-Menüs
 *
 * @param {boolean} hide - Ob das Menü ausgeblendet werden soll
 */
const manageBottomMenu = (hide) => {
  const bottomMenu = document.querySelector('.bottom-menu');
  if (bottomMenu) {
    bottomMenu.style.display = hide ? 'none' : 'flex';
  }
};

/**
 * Rendert ein Template mit Slide-Effekt (Einblenden von rechts)
 *
 * @param {TemplateResult} pageTemplate - Das lit-html Template
 * @param {Object} config - Konfigurationsoptionen
 * @returns {void}
 */
const renderWithSlide = (pageTemplate, config = {}) => {
  try {
    // Prüfen, ob bereits eine Animation läuft
    if (app.querySelector('.page-enter') || app.querySelector('.page-exit')) {
      console.log("Animation bereits in Bearbeitung, ignoriere neuen Aufruf");
      return;
    }

    // Prüfen, ob wir den Slide-Effekt überspringen sollen (nach exitWithSlide)
    if (window.skipSlideAnimation) {
      updateUI(pageTemplate, config);
      return;
    }

    // Styling zuerst auf den Container anwenden
    applyPageStyling(null, config);

    // Neues Container-Element vorbereiten
    const newContent = document.createElement('div');
    newContent.classList.add('page-content', 'page-enter');

    // Seitenname als data-Attribut setzen, falls vorhanden
    if (config.pageName) {
      newContent.setAttribute('data-page', config.pageName);
    }

    // Hintergrundfarbe explizit setzen basierend auf containerClass
    if (config.containerClass && config.containerClass.includes('white-bg')) {
      newContent.style.backgroundColor = 'white';
    } else if (config.containerClass && config.containerClass.includes('screen-centered')) {
      newContent.style.backgroundColor = 'var(--secondary-dark-green)';
    }

    // Wrapper für den Inhalt erstellen
    const contentWrapper = document.createElement('div');
    contentWrapper.classList.add('app-content-wrapper');

    // Spezielle Anpassung für healthgoalsOverview und Unterseiten
    if (config.pageName === 'healthgoals-overview' || config.pageName === 'hg-fitUmgebung') {
      contentWrapper.style.paddingTop = '0';
    }

    // In Wrapper hineinrendern
    render(pageTemplate, contentWrapper);

    // Wrapper in neues Element einfügen
    newContent.appendChild(contentWrapper);

    // In DOM einfügen
    app.appendChild(newContent);

    // Scrollposition zurücksetzen
    newContent.scrollTop = 0;

    // Animation starten im nächsten Frame
    requestAnimationFrame(() => {
      newContent.classList.add('page-enter-active');
    });

    // Nach Ende der Transition: altes Element entfernen
    newContent.addEventListener('transitionend', () => {
      // Alle alten Inhalte entfernen
      const oldContents = app.querySelectorAll('.page-content:not(.page-enter)');
      oldContents.forEach(element => element.remove());

      // Neue Seite fixieren (nicht mehr animiert)
      newContent.classList.remove('page-enter', 'page-enter-active');
      newContent.style.position = 'relative';

      // Stelle sicher, dass die Bottom-Navigation sichtbar ist
      if (!config.hideBottomMenu) {
        manageBottomMenu(false);
      }
    }, { once: true });

  } catch (error) {
    console.error("[Router Error]: Failed to render with slide", error);
  }
};

/**
 * Wendet Styling auf die Seite an
 *
 * @param {HTMLElement} contentElement - Das Content-Element
 * @param {Object} config - Konfigurationsoptionen
 */
const applyPageStyling = (contentElement, config = {}) => {
  // Update header styling
  const phoneHeader = document.querySelector(".phone-header");
  if (phoneHeader) {
    // Remove all potentially existing classes
    phoneHeader.classList.remove("primary-bg", "white-bg");

    // Add new class if specified in config
    if (config.headerClass) {
      phoneHeader.classList.add(config.headerClass);
    }
  }

  // Update container styling
  const appContainer = document.querySelector(".phone-content");
  if (appContainer) {
    // Remove all potentially existing classes
    appContainer.classList.remove("primary-bg", "white-bg", "screen-centered");

    // Add new classes if specified in config
    if (config.containerClass) {
      // Split string into individual classes (if multiple are contained)
      const classes = config.containerClass.split(" ");
      appContainer.classList.add(...classes);
    }
  }
};

/**
 * UI update middleware für normale Seitenwechsel ohne Animation
 */
const updateUI = (pageTemplate, config = {}) => {
  try {
    // Render the template
    const contentElement = document.createElement('div');
    contentElement.classList.add('page-content');
    render(pageTemplate, contentElement);

    // Alten Inhalt entfernen und neuen hinzufügen
    const oldContent = app.querySelector('.page-content');
    if (oldContent) oldContent.remove();
    app.appendChild(contentElement);

    // Styling anwenden
    applyPageStyling(contentElement, config);

  } catch (error) {
    console.error("[Router Error]: Failed to update UI", error);
  }
};

/**
 * Wartet, bis der Inhalt vollständig gerendert ist, und setzt dann die Scrollposition zurück
 * @param {string} routePath - Der Pfad der aktuellen Route
 * @param {number} maxWait - Maximale Wartezeit in Millisekunden
 */
const waitForRenderThenScrollUp = (routePath, maxWait = 500) => {
  const start = performance.now();
  const appContainer = document.querySelector(".phone-content");

  if (!appContainer) {
    return;
  }

  function tryScroll() {
    const currentTime = performance.now();
    const timeElapsed = currentTime - start;

    // Wenn die maximale Wartezeit überschritten wurde oder der Inhalt vollständig gerendert ist
    if (timeElapsed > maxWait) {
      appContainer.scrollTop = 0;
    }
    // Versuche es erneut im nächsten Frame
    else {
      // Setze die Scrollposition zurück und prüfe, ob sie bei 0 bleibt
      const currentScrollPos = appContainer.scrollTop;
      if (currentScrollPos > 0) {
        appContainer.scrollTop = 0;
        // Prüfe im nächsten Frame, ob der Scroll bei 0 geblieben ist
        requestAnimationFrame(tryScroll);
      } else {
        // Wenn die Scrollposition bereits 0 ist, prüfe, ob sie so bleibt
        requestAnimationFrame(() => {
          if (appContainer.scrollTop > 0) {
            // Wenn der Scroll wieder hochgesprungen ist, versuche es erneut
            requestAnimationFrame(tryScroll);
          }
        });
      }
    }
  }

  // Starte den ersten Versuch
  requestAnimationFrame(tryScroll);
};

/**
 * Führt eine Slide-Out-Animation aus und navigiert dann zurück
 *
 * @param {string} backUrl - Die URL, zu der navigiert werden soll
 * @returns {void}
 */
const exitWithSlide = (backUrl) => {
  try {
    // Prüfen, ob bereits eine Animation läuft
    if (app.querySelector('.page-enter') || app.querySelector('.page-exit')) {
      console.log("Animation bereits in Bearbeitung, ignoriere neuen Aufruf");
      return;
    }

    const currentContent = app.querySelector('.page-content');
    if (!currentContent) {
      // Wenn kein Content vorhanden ist, direkt navigieren
      router.navigate(backUrl);
      return;
    }

    // Slide-Out-Animation starten
    currentContent.classList.add('page-exit');

    // Animation im nächsten Frame starten
    requestAnimationFrame(() => {
      currentContent.classList.add('page-exit-active');
    });

    // Nach Ende der Transition: zur Ziel-URL navigieren und Fade-Overlay hinzufügen
    currentContent.addEventListener('transitionend', () => {
      // Alte Animation-Klassen entfernen
      currentContent.classList.remove('page-exit', 'page-exit-active');

      // Weißes Overlay erstellen
      const overlay = document.createElement('div');
      overlay.classList.add('fade-overlay');

      // Overlay zum App-Container hinzufügen
      app.appendChild(overlay);

      // Zur Ziel-URL navigieren mit einem speziellen Flag, um den Slide-Effekt zu überspringen
      window.skipSlideAnimation = true;
      router.navigate(backUrl);

      // Kurze Verzögerung, um sicherzustellen, dass die neue Seite gerendert wurde
      setTimeout(() => {
        // Overlay ausblenden
        overlay.style.opacity = '0';

        // Overlay entfernen, nachdem die Fade-Animation abgeschlossen ist
        overlay.addEventListener('transitionend', () => {
          overlay.remove();
          // Flag zurücksetzen
          window.skipSlideAnimation = false;
        }, { once: true });

        // SegmentedControl initialisieren, falls wir zur healthgoals-overview zurückkehren
        if (backUrl === "/healthgoals-overview") {
          setTimeout(() => {
            if (segmentedControl && typeof segmentedControl.initialize === 'function') {
              segmentedControl.initialize();
              console.log("[exitWithSlide] SegmentedControl initialisiert");
            }
          }, 150);
        }
      }, 150); // Erhöhe die Verzögerung für bessere Stabilität
    }, { once: true });

  } catch (error) {
    console.error("[Router Error]: Failed to exit with slide", error);
    // Bei Fehler trotzdem navigieren
    router.navigate(backUrl);
  }
};

// Router erstellen
const router = new Navigo("/", {
  hash: false,  // Wir verwenden keine Hash-Navigation, aber wir können andere Optionen nutzen
  linksSelector: "a[data-navigate]" // Automatisch Links mit data-navigate Attribut verarbeiten
});

// Routen definieren
router.on("/", () => {
  updateUI(templateAppstart01(), {
    headerClass: "", // Keine spezielle Klasse für den Header
    containerClass: "screen-centered", // spezielle Klasse für den Container
  });
  waitForRenderThenScrollUp("/");
});

router.on("/features", () => {
  updateUI(featuresTemplate(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
  });
  waitForRenderThenScrollUp("/features");
});

router.on("/healthgoals-overview", () => {
  renderWithSlide(healthgoalsOverviewWithActiveGoalTemplate(), {
    headerClass: "primary-bg",      // Setzt die Klasse für den Header
    containerClass: "white-bg",     // Setzt die Klasse für den Container
    pageName: "healthgoals-overview", // Seitenname für Identifikation
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/healthgoals-overview");

  // Initialisiere die segmentedControl nach dem Rendern mit erhöhter Verzögerung
  setTimeout(() => {
    if (segmentedControl && typeof segmentedControl.initialize === 'function') {
      segmentedControl.initialize();
      console.log("[/healthgoals-overview] SegmentedControl initialisiert");
    } else {
      console.warn("[/healthgoals-overview] SegmentedControl nicht verfügbar");
    }
  }, 100);
});

router.on("/healthgoals-overview-no-goal", () => {
  renderWithSlide(healthgoalsOverviewNoGoalTemplate(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "healthgoals-overview",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/healthgoals-overview-no-goal");

  manageBottomMenu(true);
});

// Unterseiten Healthgoals - nutzt den App-State
router.on("/healthgoals-overview/hg-fitUmgebung", () => {
  renderWithSlide(hg_fitUmgebungTemplate(appStorage.activeHealthGoals.fitUmgebung), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "hg-fitUmgebung",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/healthgoals-overview/hg-fitUmgebung");

  manageBottomMenu(true);

  // Initialisiere das Reset-Menu auch für inaktive Gesundheitsziele
  initializeHealthGoalResetMenu(appStorage.activeHealthGoals.fitUmgebung);
});

// Neue Route für die aktive Ansicht nach dem Poll
router.on("/healthgoals-overview/hg-fitUmgebung-active", () => {
  renderWithSlide(hg_fitUmgebungTemplate(true), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "hg-fitUmgebung",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/healthgoals-overview/hg-fitUmgebung-active");

  manageBottomMenu(true);

  // Initialisiere das Reset-Menu für aktive Gesundheitsziele
  initializeHealthGoalResetMenu(true);
});

router.on("/homescreen", () => {
  updateUI(homescreenTemplate(), {
    headerClass: "primary-bg", // Setzt die gewünschte Klasse
    containerClass: "white-bg", // Setzt die gewünschte Klasse
  });
  waitForRenderThenScrollUp("/homescreen");
});

router.on("/dein-bereich", () => {
  updateUI(deinBereichTemplate(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
  });
  waitForRenderThenScrollUp("/dein-bereich");

  // Initialisiere die Segmented Control nach dem Rendern
  initializeDeinBereich();
});

router.on("/settings", () => {
  updateUI(settingsTemplate(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
  });
  waitForRenderThenScrollUp("/settings");
});

// Unterseite Healthgoal Consent
router.on("/consent", () => {
  renderWithSlide(templateConsent(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "hg-consent", // Seitenname für Identifikation
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/consent");
});

// Unterseite Healthgoal Initial Niveau
router.on("/hg-niveau-initial", () => {
  updateUI(templateNiveauInitial(), {
    headerClass: "primary-bg", // Klasse für den Header
    containerClass: "white-bg", // Klasse für den Container
  });
  waitForRenderThenScrollUp("/hg-niveau-initial");
});

// Unterseite Healthgoal Poll
router.on("/hg-niveau-poll", () => {
  renderWithSlide(templateNiveauPoll(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "hg-niveau-poll",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/hg-niveau-poll");

  manageBottomMenu(true);
});

router.on("/challenge/lockere-wanderung", () => {
  renderWithSlide(templateLockereWanderung(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "challenge-lockere-wanderung",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/challenge/lockere-wanderung");

  manageBottomMenu(true);

  // Initialisiere die Segmented Control nach dem Rendern
  setTimeout(() => {
    const segmentedControlContainer = document.getElementById("segmentedControlContainer");
    if (segmentedControlContainer) {
      const tablinks = segmentedControlContainer.querySelector(".tablinks");
      if (tablinks) {
        tablinks.click();
        tablinks.classList.add("active");
        console.log("[Router] SegmentedControl für Lockere Wanderung initialisiert");
      } else {
        console.log("[Router] Tablinks nicht gefunden, versuche erneut...");
        setTimeout(() => {
          const tablinks = segmentedControlContainer.querySelector(".tablinks");
          if (tablinks) {
            tablinks.click();
            tablinks.classList.add("active");
            console.log("[Router] SegmentedControl für Lockere Wanderung initialisiert (2. Versuch)");
          }
        }, 150);
      }
    }

    // Initialisiere das Reset-Menu für die Challenge
    initializeLockereWanderungResetMenu();
  }, 200);
});

router.on("/challenge/gassi-gehen", () => {
  renderWithSlide(templateGassiGehen(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "challenge-gassi-gehen",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/challenge/gassi-gehen");

  manageBottomMenu(true);

  // Initialisiere die Segmented Control nach dem Rendern
  setTimeout(() => {
    const segmentedControlContainer = document.getElementById("segmentedControlContainer");
    if (segmentedControlContainer) {
      const tablinks = segmentedControlContainer.querySelector(".tablinks");
      if (tablinks) {
        tablinks.click();
        tablinks.classList.add("active");
        console.log("[Router] SegmentedControl für Gassi Gehen initialisiert");
      } else {
        console.log("[Router] Tablinks nicht gefunden, versuche erneut...");
        setTimeout(() => {
          const tablinks = segmentedControlContainer.querySelector(".tablinks");
          if (tablinks) {
            tablinks.click();
            tablinks.classList.add("active");
            console.log("[Router] SegmentedControl für Gassi Gehen initialisiert (2. Versuch)");
          }
        }, 150);
      }
    }

    // Initialisiere das Reset-Menu für die Challenge
    initializeGassiGehenResetMenu();
  }, 200);
});

router.on("/challenge/fahrrad-tour", () => {
  renderWithSlide(templateFahrradTour(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "challenge-fahrrad-tour",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/challenge/fahrrad-tour");

  manageBottomMenu(true);

  // Initialisiere die Segmented Control nach dem Rendern
  setTimeout(() => {
    const segmentedControlContainer = document.getElementById("segmentedControlContainer");
    if (segmentedControlContainer) {
      const tablinks = segmentedControlContainer.querySelector(".tablinks");
      if (tablinks) {
        tablinks.click();
        tablinks.classList.add("active");
        console.log("[Router] SegmentedControl für Fahrrad-Tour initialisiert");
      } else {
        console.log("[Router] Tablinks nicht gefunden, versuche erneut...");
        setTimeout(() => {
          const tablinks = segmentedControlContainer.querySelector(".tablinks");
          if (tablinks) {
            tablinks.click();
            tablinks.classList.add("active");
            console.log("[Router] SegmentedControl für Fahrrad-Tour initialisiert (2. Versuch)");
          }
        }, 150);
      }
    }

    // Initialisiere das Reset-Menu für die Challenge
    initializeFahrradTourResetMenu();
  }, 200);
});

router.on("/challenge/spazieren-gehen", () => {
  renderWithSlide(templateSpazierenGehen(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "challenge-spazieren-gehen",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/challenge/spazieren-gehen");

  manageBottomMenu(true);

  // Initialisiere die Segmented Control nach dem Rendern
  setTimeout(() => {
    const segmentedControlContainer = document.getElementById("segmentedControlContainer");
    if (segmentedControlContainer) {
      const tablinks = segmentedControlContainer.querySelector(".tablinks");
      if (tablinks) {
        tablinks.click();
        tablinks.classList.add("active");
        console.log("[Router] SegmentedControl für Spazieren gehen initialisiert");
      } else {
        console.log("[Router] Tablinks nicht gefunden, versuche erneut...");
        setTimeout(() => {
          const tablinks = segmentedControlContainer.querySelector(".tablinks");
          if (tablinks) {
            tablinks.click();
            tablinks.classList.add("active");
            console.log("[Router] SegmentedControl für Spazieren gehen initialisiert (2. Versuch)");
          }
        }, 150);
      }
    }

    // Initialisiere das Reset-Menu für die Challenge
    initializeSpazierenGehenResetMenu();
  }, 200);
});

router.on("/challenge/plogging", () => {
  renderWithSlide(templatePlogging(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "challenge-plogging",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/challenge/plogging");

  manageBottomMenu(true);

  // Initialisiere die Segmented Control nach dem Rendern
  setTimeout(() => {
    const segmentedControlContainer = document.getElementById("segmentedControlContainer");
    if (segmentedControlContainer) {
      const tablinks = segmentedControlContainer.querySelector(".tablinks");
      if (tablinks) {
        tablinks.click();
        tablinks.classList.add("active");
        console.log("[Router] SegmentedControl für Plogging initialisiert");
      } else {
        console.log("[Router] Tablinks nicht gefunden, versuche erneut...");
        setTimeout(() => {
          const tablinks = segmentedControlContainer.querySelector(".tablinks");
          if (tablinks) {
            tablinks.click();
            tablinks.classList.add("active");
            console.log("[Router] SegmentedControl für Plogging initialisiert (2. Versuch)");
          }
        }, 150);
      }
    }

    // Initialisiere das Reset-Menu für die Challenge
    initializePloggingResetMenu();
  }, 200);
});

router.on("/training/treppensteigen", () => {
  renderWithSlide(templateTreppensteigen(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "training-treppensteigen",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/training/treppensteigen");

  manageBottomMenu(true);
});

router.on("/training/lockere-wanderung", () => {
  renderWithSlide(templateTrainingLockereWanderung(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "training-lockere-wanderung",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/training/lockere-wanderung");

  manageBottomMenu(true);
});

router.on("/training/gassi-gehen", () => {
  renderWithSlide(templateGassigehen(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "training-gassi-gehen",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/training/gassi-gehen");

  manageBottomMenu(true);
});

router.on("/training/fahrrad-tour", () => {
  renderWithSlide(templateTrainingFahrradTour(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "training-fahrrad-tour",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/training/fahrrad-tour");

  manageBottomMenu(true);
});

router.on("/training/spazieren-gehen", () => {
  renderWithSlide(templateTrainingSpazierenGehen(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "training-spazieren-gehen",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/training/spazieren-gehen");

  manageBottomMenu(true);
});

router.on("/training/plogging", () => {
  renderWithSlide(templateTrainingPlogging(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "training-plogging",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/training/plogging");

  manageBottomMenu(true);
});

router.on("/tracker-connect", () => {
  renderWithSlide(trackerConnectTemplate(), {
    headerClass: "primary-bg",
    containerClass: "white-bg",
    pageName: "tracker-connect",
    hideBottomMenu: false
  });
  waitForRenderThenScrollUp("/tracker-connect");
});

// Komoot Login Seite
router.on("/komoot-login", () => {
  renderWithSlide(komootLoginTemplate(), {
    headerClass: "",
    containerClass: "white-bg",
    pageName: "komoot-login",
    hideBottomMenu: true
  });
  waitForRenderThenScrollUp("/komoot-login");

  manageBottomMenu(true);
});

// Exportiere den Router und die exitWithSlide-Funktion
export { router, exitWithSlide };
