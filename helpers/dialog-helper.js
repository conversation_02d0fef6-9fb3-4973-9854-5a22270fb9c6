import { render } from "lit-html";
import { dialog } from "../webcomponents/dialog.js";

/**
 * <PERSON><PERSON><PERSON> einen Dialog an und verwaltet dessen Lebenszyklus
 *
 * @param {Object} options - Dialog-Optionen
 * @param {string} options.title - Dialog-Titel
 * @param {string} [options.text] - Optionaler Dialog-Text
 * @param {string} [options.icon] - Optionaler Pfad zum Icon
 * @param {string} [options.iconColor] - Optionale Hintergrundfarbe für die Icon-Bubble
 * @param {string} [options.iconCorner] - Optionaler Eckenstil für die Icon-Bubble ('round', 'top-left', 'top-right', 'bottom-left', 'bottom-right')
 * @param {string} [options.image] - Optionaler Pfad zum Bild
 * @param {Object} [options.buttonPrimary] - Optionale Konfiguration für den primären Button
 * @param {string} options.buttonPrimary.text - Button-Text
 * @param {Function} [options.buttonPrimary.onClick] - Optionaler Click-Handler
 * @param {string} [options.buttonPrimary.link] - Optionaler Navigations-Link
 * @param {Object} [options.buttonSecondary] - Optionale Konfiguration für den sekundären Button
 * @param {string} options.buttonSecondary.text - Button-Text
 * @param {Function} [options.buttonSecondary.onClick] - Optionaler Click-Handler
 * @param {string} [options.buttonSecondary.link] - Optionaler Navigations-Link
 * @param {Object} [options.buttonTertiary] - Optionale Konfiguration für den tertiären Button
 * @param {string} options.buttonTertiary.text - Button-Text
 * @param {Function} [options.buttonTertiary.onClick] - Optionaler Click-Handler
 * @param {string} [options.buttonTertiary.link] - Optionaler Navigations-Link
 */
export const showDialog = ({
  title,
  text,
  icon,
  iconColor,
  iconCorner,
  image,
  buttonPrimary,
  buttonSecondary,
  buttonTertiary
}) => {
  // Den .page-content Container finden, der direkt unter #app liegt
  const pageContentContainer = document.querySelector('.page-content');
  if (!pageContentContainer) {
    console.error('Dialog-Helper: .page-content Container nicht gefunden');
    return;
  }

  // Dialog-Container erstellen
  const dialogContainer = document.createElement('div');
  dialogContainer.id = 'dialog-container';
  dialogContainer.className = 'dialog-container-wrapper';

  // Overlay erstellen
  const overlay = document.createElement('div');
  overlay.id = 'dialog-overlay';
  overlay.className = 'dialog-overlay';

  // Funktion zum Schließen des Dialogs
  const closeDialog = () => {
    pageContentContainer.removeChild(dialogContainer);
    pageContentContainer.removeChild(overlay);
  };

  // Elemente zum .page-content Container hinzufügen
  pageContentContainer.appendChild(overlay);
  pageContentContainer.appendChild(dialogContainer);

  // Dialog rendern
  render(dialog({
    title,
    text,
    icon,
    iconColor,
    iconCorner,
    image,
    buttonPrimary,
    buttonSecondary,
    buttonTertiary,
    onClose: closeDialog
  }), dialogContainer);
};
