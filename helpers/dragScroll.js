/**
 * Ermöglicht Drag-Scrolling für horizontale Container
 * Fügt Ma<PERSON>-Events zu allen Elementen mit der angegebenen Klasse hinzu
 * 
 * @param {string} containerSelector - CSS-Selektor für die scrollbaren Container
 */
export const initDragScroll = (containerSelector = '.image-card-narrow-container') => {
  // Funktion zum Hinzufügen der Event-Listener
  const addDragScrolling = () => {
    const containers = document.querySelectorAll(containerSelector);
    
    containers.forEach(container => {
      // Prüfen, ob der Container bereits initialisiert wurde
      if (container.getAttribute('data-drag-initialized') === 'true') {
        // Wichtig: Wir entfernen das Attribut, um eine Neuinitialisierung zu erzwingen
        // wenn die Seite über einen Link geladen wurde
        container.removeAttribute('data-drag-initialized');
      }
      
      // Stelle sicher, dass der Container scrollbar ist
      if (getComputedStyle(container).overflowX === 'hidden') {
        container.style.overflowX = 'auto';
      }
      
      // Stelle sicher, dass smooth scrolling aktiviert ist
      container.style.scrollBehavior = 'smooth';
      
      let isDown = false;
      let startX;
      let scrollLeft;
      let startTime;
      let velocity = 0;
      let lastX;
      let animationFrame;

      // Event-Listener entfernen, falls sie bereits existieren
      container.removeEventListener('mousedown', handleMouseDown);
      container.removeEventListener('mouseleave', handleMouseLeave);
      container.removeEventListener('mouseup', handleMouseUp);
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
      container.removeEventListener('wheel', handleWheel);
      
      // Event-Listener-Funktionen definieren
      function handleMouseDown(e) {
        isDown = true;
        container.style.cursor = 'grabbing';
        startX = e.pageX - container.offsetLeft;
        lastX = startX;
        scrollLeft = container.scrollLeft;
        startTime = Date.now();
        velocity = 0;
        
        // Animation stoppen, wenn eine läuft
        if (animationFrame) {
          cancelAnimationFrame(animationFrame);
          animationFrame = null;
        }
        
        e.preventDefault();
      }
      
      function handleMouseLeave() {
        if (!isDown) return;
        isDown = false;
        container.style.cursor = 'grab';
        startInertialScroll();
      }
      
      function handleMouseUp() {
        if (!isDown) return;
        isDown = false;
        container.style.cursor = 'grab';
        startInertialScroll();
      }
      
      function handleMouseMove(e) {
        if (!isDown) return;
        e.preventDefault();
        const x = e.pageX - container.offsetLeft;
        const walk = (x - startX); // Reduzierte Scroll-Geschwindigkeit für mehr Kontrolle
        
        // Berechne Geschwindigkeit
        const now = Date.now();
        const elapsed = now - startTime;
        if (elapsed > 0) {
          velocity = (lastX - x) / elapsed * 15; // Multiplikator für Trägheitseffekt
        }
        lastX = x;
        startTime = now;
        
        container.scrollLeft = scrollLeft - walk;
      }
      
      // Trägheitsscrolling starten
      function startInertialScroll() {
        if (Math.abs(velocity) < 0.5) return; // Ignoriere sehr kleine Bewegungen
        
        let deceleration = 0.95; // Abbremsfaktor (höher = längeres Gleiten)
        let currentVelocity = velocity;
        
        function inertialScroll() {
          if (Math.abs(currentVelocity) < 0.5) {
            cancelAnimationFrame(animationFrame);
            animationFrame = null;
            return;
          }
          
          container.scrollLeft += currentVelocity;
          currentVelocity *= deceleration;
          animationFrame = requestAnimationFrame(inertialScroll);
        }
        
        animationFrame = requestAnimationFrame(inertialScroll);
      }
      
      // Touch-Events für mobile Geräte
      function handleTouchStart(e) {
        const touch = e.touches[0];
        handleMouseDown({
          pageX: touch.pageX,
          preventDefault: () => e.preventDefault()
        });
      }
      
      function handleTouchMove(e) {
        if (!isDown) return;
        const touch = e.touches[0];
        handleMouseMove({
          pageX: touch.pageX,
          preventDefault: () => e.preventDefault()
        });
      }
      
      function handleTouchEnd() {
        handleMouseUp();
      }
      
      // Mausrad-Event-Handler
      function handleWheel(e) {
        if (e.deltaY !== 0) {
          e.preventDefault();
          container.scrollLeft += e.deltaY;
        }
      }
      
      // Event-Listener hinzufügen
      container.addEventListener('mousedown', handleMouseDown);
      container.addEventListener('mouseleave', handleMouseLeave);
      container.addEventListener('mouseup', handleMouseUp);
      container.addEventListener('mousemove', handleMouseMove);
      
      // Touch-Events hinzufügen
      container.addEventListener('touchstart', handleTouchStart, { passive: false });
      container.addEventListener('touchmove', handleTouchMove, { passive: false });
      container.addEventListener('touchend', handleTouchEnd);
      
      // Mausrad-Event für sanftes horizontales Scrollen
      container.addEventListener('wheel', handleWheel, { passive: false });
      
      // Markiere den Container als initialisiert
      container.setAttribute('data-drag-initialized', 'true');
    });
  };

  // Sofort versuchen, die Container zu initialisieren
  addDragScrolling();
  
  // Auch nach dem DOMContentLoaded-Event versuchen (falls die Funktion vorher aufgerufen wurde)
  document.addEventListener('DOMContentLoaded', addDragScrolling);
  
  // Zusätzlich nach einer kurzen Verzögerung versuchen (für dynamisch geladene Inhalte)
  setTimeout(addDragScrolling, 500);
  
  // Wichtig: Füge einen Event-Listener für Routenwechsel hinzu
  // Dies ist ein benutzerdefiniertes Event, das wir in router.js auslösen müssen
  document.addEventListener('route-changed', addDragScrolling);
  
  // Zusätzlich nach einer längeren Verzögerung versuchen (für langsam geladene Inhalte)
  setTimeout(addDragScrolling, 1000);
};

// Exportiere eine Funktion, die das Drag-Scrolling für neu geladene Inhalte initialisiert
export const refreshDragScroll = (containerSelector = '.image-card-narrow-container') => {
  const containers = document.querySelectorAll(containerSelector);
  containers.forEach(container => {
    // Entferne das Attribut, um eine Neuinitialisierung zu erzwingen
    container.removeAttribute('data-drag-initialized');
  });
  
  // Initialisiere das Drag-Scrolling neu
  initDragScroll(containerSelector);
};
