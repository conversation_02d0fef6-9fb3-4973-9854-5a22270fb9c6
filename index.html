<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self' file:; script-src 'self' 'unsafe-inline' 'unsafe-eval' file:; style-src 'self' 'unsafe-inline';"
    />
    <title>Navida LIT-html</title>
    <base href="/" />
    <link
      rel="stylesheet"
      type="text/css"
      media="screen"
      href="./variables.css"
    />
    <link rel="stylesheet" type="text/css" media="screen" href="./fonts.css" />
    <link
      rel="stylesheet"
      type="text/css"
      media="screen"
      href="./animation.css"
    />
    <link
      rel="stylesheet"
      type="text/css"
      media="screen"
      href="./components.css"
    />
    <link rel="stylesheet" type="text/css" media="screen" href="./styles.css" />
  </head>
  <body>
    <!-- Screen Fadeout Animation <div id="fadeout"></div> -->
    <div class="center-phone">
      <div class="phone-frame">
        <div class="phone-container">
          <div class="phone-header">
            <div id="app-time">
              <!-- Daytime via clock.js -->
            </div>
            <div>
              <img src="svg/Cellular Connection.svg" alt="Empfang" />
              <img src="svg/Wifi.svg" alt="WLAN" />
              <img src="svg/Battery.svg" alt="Akku" />
            </div>
          </div>
          <div class="phone-content screen-centered">
            <div id="app">
              <!-- App Rendering here -->
            </div>
          </div>
          <div class="phone-footer">
            <div class="button-back">
              <img
                src="svg/icons/icon_button_return.svg"
                alt="Zurück"
                id="footer-back-button"
              />
            </div>
            <div class="button-home">
              <img
                data-navigate="/"
                src="svg/icons/icon_button_home.svg"
                alt="Startseite"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <script type="module" src="./index.js"></script>
  </body>
</html>
<script>
  console.log("App loaded");
</script>
