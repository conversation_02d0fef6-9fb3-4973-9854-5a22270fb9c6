# Aktuelle ToDos für Feature: [Lockere Wanderung Training wurde übertragen]

## Allgemeine Hinweise für die KI

- Schreibe alle neuen Komponenten in lit-html.
- Speichere alle neuen Komponenten in /webcomponents
- Nutze bereits bestehende Komponenten bevor du neue Komponenten baust in /webcomponents
- Nutze camelCase für Variablen und Komponenten.
- Nutze kebabCase für CSS-Klassen.
- Nutze vorhandene CSS-Variablen von /variables.css
- Nutze die Schriftarten von /fonts.css
- Nutze die allgemeinen CSS Klassen von /styles.css und für Komponenten immer eine eigene CSS-Datei
- Berücksichtige bestehende Hilfsfunktionen aus /helpers
- Füge Kommentare hinzu, wenn Logik nicht selbsterklärend ist.
- Entferne ungenutzte Variablen und Imports aus der Seite auf der du gerade arbeitest
- Lies die /readme.md

---

## Beschreibung der Aufgabe
Auf der Challenge Seite /challenge/lockere-wanderung wird im oberen Bereich eine progressGaugeCard angezeigt, die den Fortschritt der Challenge anzeigt, sofern die Challenge gestartet wurde. Diese beginnt initial mit 0% und 0 von 2 Trainings erfasst.
Weiter unten auf der Seite hat der Nutzer über den Button "Training erfassen" die Möglichkeit, ein Training zu erfassen. Sobald ein Training erfasst wurde, soll die progressGaugeCard aktualisiert werden und der Fortschritt angepasst werden. Das Training wird erfasst, wenn der Nutzer im overlay über den primary button bestätigt, dass er das Training absolviert hat. Hierzu sollen im storage auch entsprechende Werte hinterlegt werden: wie viele Trainings erfasst wurden und wie viele Trainings insgesamt zu erledigen sind.
Anschließend soll die Seite ganz nach oben scrollen und der Nutzer soll wieder den Fortschritt der Challenge sehen. Orientiere dich beim scrollen an der bereits auf anderen Seiten, wie /hg-consent ,implementierten Methode scrollToSaveButton beim Aufbau dieses automatischen Scrollens.
Wenn das scrolling abgeschlossen ist, soll sich die Zahl der erfassten Trainings in der progressGaugeCard um 1 erhöhen. Der Prozentwert soll von 0% auf 50% angehoben werden, wobei die Zahlen von 0 bis 50 in einer kleinen animation innerhalb von 2 Sekunden hochgezählt werden soll. Außerdem muss der grüne arc der progressGaugeCard von 0% auf 50% animiert werden. Orientiere dich hierfür an der bereits implementierten progressGaugeCard in /webcomponents/progressGaugeCard.js und der dort implementierten Animation des arcs.
Außerdem bekommt unten in der Seite der .training-done-container eine andere Klasse .training-done-container-inactive, die die Hintergrundfarbe auf dunkelgrau setzt. Der darin enthaltene Text wird geändert zu "Du hast Dein Training für heute bereits erfasst. Morgen kannst Du die nächste Tageschallenge absolvieren und hier erfassen." und der primary button verschwindet.

---

## Aufgaben

1. [ ] Trainingserfassung implementieren
    1.1 [ ] Nach Klick auf den primary button im overlay wird ein neues Training erfasst
    1.2 [ ] Nach dem Klick auf den primary button im overlay wird die Seite nach oben gescrollt
2. [ ] Trainingserfassung in Storage speichern
    2.1 [ ] Anlegen einer neuen Variable "completedTrainings" in appStorage, die die Anzahl der erfassten Trainings speichert
    2.2 [ ] Anlegen einer neuen Variable "totalTrainings" in appStorage, die die Gesamtanzahl der Trainings speichert
    2.3 [ ] Beide Variablen müssen der Challenge zugeordnet werden, damit man immer auslesen kann, für welche Challenge die Werte gelten
3. [ ] Trainingserfassung in progressGaugeCard anzeigen
    3.1 [ ] Die Anzahl der erfassten Trainings wird aktualisiert
    3.2 [ ] Der Prozentwert wird aktualisiert indem die Zahlen von 0 bis 50 in einer kleinen animation innerhalb von 2 Sekunden hochgezählt werden
    3.3 [ ] Der grüne Arc wird animiert
5. [ ] Trainingserfassung abschließen und Button deaktivieren
    5.1 [ ] Die Anzahl der erfassten Trainings darf nicht die maximale Anzahl der Trainings überschreiten
    5.2 [ ] Der container mit der Klasse .training-done-container bekommt die Klasse .training-done-container-inactive 
    5.3 [ ] Der Text in der .training-done-container wird geändert zu "Du hast Dein Training für heute bereits erfasst. Morgen kannst Du die nächste Tageschallenge absolvieren und hier erfassen."
    5.4 [ ] Der primary button im .training-done-container verschwindet
6. [ ] Dokumentation für das neue Feature ergänzen
7. [ ] Code-Review und Refactoring
