/* Fade-Overlay für sanften Übergang */
.fade-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  opacity: 1;
  transition: opacity 0.5s ease;
  z-index: 100;
  pointer-events: none; /* Damit Klicks durchgehen */
}

* {
  box-sizing: border-box;
}

body {
  background-color: white;
  height: 98vh;
  overflow: hidden;
}

body p:hover {
  /* Don't use Mouse Text Pointer on Text */
  cursor: default;
}

/* Set pointer cursor for paragraphs inside links */
a p:hover {
  cursor: pointer;
}

nav {
  position: absolute;
}

/* Phone */
.center-phone {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.phone-frame {
  width: var(--phone-screen-width);
  height: auto;
  background-color: black;
  border-radius: 42px;
  padding: 10px;
  margin: 0 auto;
  transform: scale(0.8);
  transform-origin: center center;
  box-sizing: content-box;
}

.phone-container {
  width: var(--phone-screen-width);
  height: calc(var(--phone-screen-height) + var(--phone-footer-height));
  background-color: var(--aok-grn-1);
  border-radius: 32px;
  overflow: hidden;
}

.phone-header {
  height: var(--phone-header-height);
  background-color: var(--secondary-dark-green);
  color: white;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
}

.phone-header p {
  color: white;
  text-align: center;
  font-family: sans-serif;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: -0.3px;
}

.phone-content {
  height: calc(var(--phone-screen-height) - var(--phone-header-height));
  overflow-y: scroll;
  scrollbar-width: none;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: flex-start;
  padding: 0;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.phone-content::-webkit-scrollbar {
  display: none;
}

/* Bottom Navigation styles moved to components.css */

/* Footer */
.phone-footer {
  height: var(--phone-footer-height);
  background-color: var(--primary-brand);
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.phone-footer img {
  height: 1rem;
  width: auto;
  color: white;
}

.phone-footer .button-back,
.phone-footer .button-home {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
}

.phone-footer .button-back:hover,
.phone-footer .button-home:hover {
  opacity: 0.8;
  cursor: pointer;
}

/* Neuer Stil für den Zurück-Button ohne data-navigate Attribut */
.phone-footer .button-back img {
  cursor: pointer;
}

#app {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0 0; /* Padding entfernen */
  color: white;
  justify-content: flex-start;
}

/* Neuer Container für den Inhalt */
.app-content-wrapper {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  width: 100%;
}

.screen-centered {
  width: 100%;
  background-color: var(--secondary-dark-green);
}

/* This is overwriting too much standard divs, so use .standard-container instead on the items themselves
#app
  > div:not(.bottom-menu):not(.top-menu-container):not(
    .feature-cards-small-container
  ) {
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
} */

.standard-container {
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
}

.screen-centered > #app {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 32px 0;
  color: white;
  height: 100%;
}

.startAnimation {
  opacity: 0;
  animation: fadeInUp 2s ease-in 1s;
  animation-fill-mode: forwards;
}

/* General */
.full-width {
  width: 100%;
}

.flex-full-width {
  align-items: stretch;
}

.content-padding {
  padding-left: 24px;
  padding-right: 24px;
}

.content-widening {
  padding-top: 80px;
  padding-bottom: 80px;
}

.content-left-align {
  align-items: flex-start !important;
}

.light-grn-text {
  color: var(--primary-light-grn);
}

.dark-grn-text {
  color: var(--primary-brand);
}

.contrast-grn-text {
  color: var(--primary-contrast);
}

.black-text {
  color: var(--primary-text);
}

/* Button styles moved to components.css */

button:focus,
.button-primary:focus {
  border-color: var(--button-primary-background-pressed);
  background-color: white;
}

.button-bottom {
  margin-top: auto;
}

/* Inputs */
input {
  all: unset;
  display: flex;
  width: 100%;
  height: 3rem;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid var(--text-input-border-active);
  overflow: hidden;
  color: var(--text-input-text-active);
  text-overflow: ellipsis;
  font-family: "AOK Buenos Aires Text";
  font-size: 1rem;
  font-style: normal;
  font-weight: 400;
  line-height: 1.35rem;
  letter-spacing: 0.15px;
  box-sizing: border-box;
}

input:focus {
  color: var(--text-input-text-focus);
  border-color: var(--text-input-border-focus);
}

input.on-bg,
input.contrast {
  border-color: white;
  color: white;
}

/* Startseite */
.top-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 24px;
  align-items: center;
}

.green-bg {
  background-color: var(--secondary-dark-green);
  color: white;
}

.main-container {
  background-color: white;
  color: var(--primary-text);
  padding: 16px;
  flex-grow: 1;
}

.lightgrn-color {
  color: var(--primary-light-grn);
}

/* List styles moved to components.css */

.no-url-style {
  text-decoration: none; /* Entfernt die Unterstreichung */
  color: inherit; /* Übernimmt die Textfarbe des übergeordneten Elements */
  background: none; /* Entfernt den Hintergrund */
  border: none; /* Entfernt den Rahmen */
  cursor: pointer; /* Beibehaltung des Zeigers für die Klickelemente */
}

/* phone header app colors */
.primary-bg {
  background-color: var(--primary-brand);
}

/* App Container app colors */
.white-bg {
  background-color: white;
}

/* Use Darkgreen Text */
.green-headline {
  color: var(--primary-brand);
}

.green-text {
  color: var(--primary-brand);
}

.uppercase {
  text-transform: uppercase;
}

/* Center Text */
.center-text > h1,
.center-text > h2,
p.center-text {
  text-align: center;
}

.text-align-left {
  text-align: left;
}

/* Left indent - Texteinzug */
.left-indent {
  margin-left: 36px;
}

/* Progress Gauge Card */
:root {
  /* ... Bestehende Variablen ... */
  
  /* Progress Gauge Card */
  --cards-progress-gauge-card-background-success: #E8F4F2;
  --cards-progress-gauge-card-background-warning: #FFF8E6;
  --cards-progress-gauge-card-background-error: #FFEBEB;
  --cards-progress-gauge-card-text: #005E3F;
}

/* Content Separation */
/* Content box styles moved to components.css */

.content-no-bottom-padding, .tabpadding > .content-no-bottom-padding {
  padding-bottom: 0px;
}

.content-no-top-padding, .tabpadding > .content-no-top-padding {
  padding-top: 0px;
}

.content-bottom-padding {
  padding-bottom: 24px;
}

.content-top-padding {
  padding-top: 24px;
}

.content-bottom-small-padding {
  padding-bottom: 16px;
}

.content-no-side-padding, .tabpadding > .content-no-side-padding {
  padding-left: 0px;
  padding-right: 0px;
}

.content-no-bottom-margin,
.content-no-bottom-margin > h1,
.content-no-bottom-margin > h2 {
  margin-bottom: 0;
}

.gap-items-24 {
  gap: 24px;
}

.gap-items-16 {
  gap: 16px;
}

/* Cards, Feature Cards Container, and Poll Container styles moved to components.css */

/* Poll Image */
.poll-image {
  margin: 12px 24px;
}

/* Media Queries */
@media (min-height: 900px) {
  .phone-frame {
    transform: scale(1);
  }
}

/* Animation für Seitenwechsel */
.phone-content {
  position: relative;
  overflow: hidden;
}

.page-content {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  transition: transform 0.4s ease;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 32px 0;
  box-sizing: border-box;
  scrollbar-width: none;
  -ms-overflow-style: none;
  background-color: inherit;
}

.page-content::-webkit-scrollbar {
  display: none;
}

/* Einblenden von rechts */
.page-enter {
  transform: translateX(100%);
}

.page-enter-active {
  transform: translateX(0);
}

/* Ausblenden nach rechts */
.page-exit {
  transform: translateX(0);
}

.page-exit-active {
  transform: translateX(100%);
}

/* Stelle sicher, dass die Bottom-Navigation immer sichtbar ist */
.bottom-menu {
  position: fixed;
  bottom: 40px;
  left: 10px;
  right: 10px;
  z-index: 100;
}

/* Stelle sicher, dass der letzte Container genug Abstand nach unten hat */
/* Nur Abstand hinzufügen, wenn .bottom-menu existiert */
body:has(.bottom-menu) #app > *:last-child:not(.bottom-menu),
body:has(.bottom-menu) .last-container {
  padding-bottom: 48px;
}

/* Kein zusätzlicher Abstand, wenn .bottom-menu nicht existiert */
body:not(:has(.bottom-menu)) #app > *:last-child,
body:not(:has(.bottom-menu)) .last-container {
  padding-bottom: 32px;
}
