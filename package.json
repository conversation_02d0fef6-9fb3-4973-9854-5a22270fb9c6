{"name": "navid<PERSON><PERSON>", "version": "1.0.0", "description": "lit-html version for NavidaWeb", "scripts": {"start": "parcel index.html --dist-dir dist-dev --no-cache", "build": "parcel build index.html --dist-dir dist-build --no-scope-hoist --no-cache --public-url ./", "clean": "rm -rf dist-build", "sync:ios": "npx cap sync ios", "open:ios": "npx cap open ios", "ios:all": "npm run build && npm run sync:ios && npm run open:ios"}, "author": "", "license": "ISC", "dependencies": {"@capacitor/cli": "^6.2.0", "@capacitor/core": "^6.2.0", "@capacitor/ios": "^6.2.0", "lit-html": "^3.2.1", "navigo": "^8.11.1"}, "devDependencies": {"parcel": "^2.13.3", "plist": "^3.1.0", "svgo": "^3.3.2"}}