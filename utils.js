/**
 * Global reference to the app element
 * @type {HTMLElement}
 */
export const app = document.getElementById("app");

/**
 * Global application settings
 * @type {Object}
 * @property {string} theme - Current theme ('light' or 'dark')
 * @property {string} language - Current language code
 */
export const globalSettings = {
  theme: "light",
  language: "de",
};

/**
 * Error severity levels
 * @enum {string}
 */
export const ErrorLevel = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  CRITICAL: 'critical'
};

/**
 * Logs an error message to the console with appropriate formatting
 * @param {string} message - The error message to log
 * @param {ErrorLevel} [level=ErrorLevel.ERROR] - The severity level of the error
 * @param {Error} [originalError] - The original error object if available
 */
export const logError = (message, level = ErrorLevel.ERROR, originalError = null) => {
  const prefix = `[App ${level.toUpperCase()}]`;

  switch (level) {
    case ErrorLevel.INFO:
      console.info(`${prefix}: ${message}`);
      break;
    case ErrorLevel.WARNING:
      console.warn(`${prefix}: ${message}`);
      break;
    case ErrorLevel.ERROR:
    case ErrorLevel.CRITICAL:
      console.error(`${prefix}: ${message}`);
      if (originalError) {
        console.error('Original error:', originalError);
      }
      break;
    default:
      console.error(`${prefix}: ${message}`);
  }
};

/**
 * Safely executes a function and handles any errors that occur
 * @param {Function} fn - The function to execute
 * @param {Array} [args=[]] - Arguments to pass to the function
 * @param {string} [errorMessage='An error occurred'] - Custom error message
 * @param {ErrorLevel} [level=ErrorLevel.ERROR] - The severity level of the error
 * @returns {*} The result of the function or null if an error occurred
 */
export const trySafe = (fn, args = [], errorMessage = 'An error occurred', level = ErrorLevel.ERROR) => {
  try {
    return fn(...args);
  } catch (error) {
    logError(errorMessage, level, error);
    return null;
  }
};

/**
 * Validates user input against specified constraints
 * @param {*} value - The value to validate
 * @param {Object} options - Validation options
 * @param {boolean} [options.required=false] - Whether the value is required
 * @param {number} [options.minLength] - Minimum length for string values
 * @param {number} [options.maxLength] - Maximum length for string values
 * @param {RegExp} [options.pattern] - Regular expression pattern to match
 * @param {Function} [options.customValidator] - Custom validation function
 * @returns {Object} Validation result with isValid and message properties
 */
export const validateInput = (value, options = {}) => {
  const { required = false, minLength, maxLength, pattern, customValidator } = options;

  // Check if required
  if (required && (value === null || value === undefined || value === '')) {
    return { isValid: false, message: 'This field is required' };
  }

  // Skip other validations if value is empty and not required
  if (value === null || value === undefined || value === '') {
    return { isValid: true, message: '' };
  }

  // Check min length
  if (minLength !== undefined && typeof value === 'string' && value.length < minLength) {
    return { isValid: false, message: `Minimum length is ${minLength} characters` };
  }

  // Check max length
  if (maxLength !== undefined && typeof value === 'string' && value.length > maxLength) {
    return { isValid: false, message: `Maximum length is ${maxLength} characters` };
  }

  // Check pattern
  if (pattern && typeof value === 'string' && !pattern.test(value)) {
    return { isValid: false, message: 'Invalid format' };
  }

  // Run custom validator if provided
  if (customValidator && typeof customValidator === 'function') {
    const customResult = customValidator(value);
    if (customResult !== true) {
      return { isValid: false, message: customResult || 'Invalid value' };
    }
  }

  return { isValid: true, message: '' };
};

/**
 * Zentraler App-Speicher mit Persistenz
 * Verwaltet alle App-Daten und speichert sie im localStorage
 */
export const appStorage = {
  // Interner Speicher
  _data: {
    // Benutzerdaten
    user: {
      name: null,
    },
    // Verbundene Apps
    connectedApps: {},
    // Challenge-Daten
    challenges: {},
    // Aktive Gesundheitsziele
    activeHealthGoals: {
      fitUmgebung: false,
      aktivInDerNatur: false
    },
    // Healthgoal-Niveau: "beginner", "intermediate", "advanced" (legacy - wird durch healthGoalLevels ersetzt)
    healthGoalLevel: null,
    // Per-Health-Goal Niveaus
    healthGoalLevels: {},
    // Aktuell in Bearbeitung befindliches Gesundheitsziel (für Niveau-Poll-Flow)
    currentHealthGoalContext: null
  },

  /**
   * Initialisiert den Speicher beim App-Start
   */
  init() {
    try {
      // Versuche, gespeicherte Daten aus dem localStorage zu laden
      const savedData = localStorage.getItem('appData');
      if (savedData) {
        this._data = JSON.parse(savedData);
        console.log('App-Daten aus localStorage geladen');
      }
    } catch (error) {
      logError('Fehler beim Laden der App-Daten', ErrorLevel.WARNING, error);
    }

    // Stelle sicher, dass alle erforderlichen Objekte existieren
    this._ensureDataStructure();

    // Initialisiere alle Challenges mit korrekten Standardwerten
    this._initializeChallenges();

    // Mache appStorage und Debug-Funktionen global verfügbar für Debugging
    window.appStorage = this;
    window.debugHealthGoals = debugHealthGoals;
  },

  /**
   * Stellt sicher, dass alle erforderlichen Datenstrukturen existieren
   * @private
   */
  _ensureDataStructure() {
    if (!this._data.user) this._data.user = { name: null };
    if (!this._data.connectedApps) this._data.connectedApps = {};
    if (!this._data.challenges) this._data.challenges = {};
    if (!this._data.activeHealthGoals) this._data.activeHealthGoals = { fitUmgebung: false, aktivInDerNatur: false };
    if (!this._data.hasOwnProperty('healthGoalLevel')) this._data.healthGoalLevel = null;
    if (!this._data.healthGoalLevels) this._data.healthGoalLevels = {};
    if (!this._data.hasOwnProperty('currentHealthGoalContext')) this._data.currentHealthGoalContext = null;
  },

  /**
   * Initialisiert alle Challenges mit korrekten Standardwerten
   * @private
   */
  _initializeChallenges() {
    Object.keys(CHALLENGE_CONFIG).forEach(challengeKey => {
      if (!this._data.challenges[challengeKey]) {
        const config = CHALLENGE_CONFIG[challengeKey];
        this._data.challenges[challengeKey] = {
          started: false,
          completedTrainings: 0,
          totalTrainings: config.totalTrainings,
          completed: false
        };
      } else {
        // Stelle sicher, dass bestehende Challenges die korrekten totalTrainings haben
        const config = CHALLENGE_CONFIG[challengeKey];
        if (this._data.challenges[challengeKey].totalTrainings !== config.totalTrainings) {
          this._data.challenges[challengeKey].totalTrainings = config.totalTrainings;
        }
        // Stelle sicher, dass das completed-Flag existiert
        if (!this._data.challenges[challengeKey].hasOwnProperty('completed')) {
          const challengeData = this._data.challenges[challengeKey];
          const completionPercentage = (challengeData.completedTrainings / challengeData.totalTrainings) * 100;
          challengeData.completed = completionPercentage >= 80;
        }
      }
    });
    this._save();
  },

  /**
   * Speichert Änderungen im localStorage
   * @private
   */
  _save() {
    try {
      localStorage.setItem('appData', JSON.stringify(this._data));
    } catch (error) {
      logError('Fehler beim Speichern der App-Daten', ErrorLevel.WARNING, error);
    }
  },

  /**
   * Getter für Benutzername
   */
  get userName() {
    return this._data.user.name || "Nutzer";
  },

  /**
   * Setter für Benutzername
   */
  set userName(name) {
    this._data.user.name = name;
    this._save();
  },

  /**
   * Getter für verbundene Apps
   */
  get connectedApps() {
    return this._data.connectedApps;
  },

  /**
   * Getter für Challenges
   */
  get challenges() {
    return this._data.challenges;
  },

  /**
   * Getter für aktive Gesundheitsziele
   */
  get activeHealthGoals() {
    return this._data.activeHealthGoals;
  },

  /**
   * Verbindet oder trennt eine Fitness-App
   * @param {string} appName - Name der App
   * @param {boolean} isConnected - Verbindungsstatus
   */
  setAppConnection(appName, isConnected) {
    this._data.connectedApps[appName] = isConnected;
    this._save();
  },

  /**
   * Aktiviert ein Gesundheitsziel
   * @param {string} goalName - Name des Ziels
   */
  activateHealthGoal(goalName) {
    if (this._data.activeHealthGoals.hasOwnProperty(goalName)) {
      this._data.activeHealthGoals[goalName] = true;
      this._save();
    }
  },

  /**
   * Setzt oder aktualisiert Challenge-Daten
   * @param {string} challengeName - Name der Challenge
   * @param {Object} data - Challenge-Daten
   */
  updateChallenge(challengeName, data) {
    // Stelle sicher, dass die Challenge mit korrekten totalTrainings initialisiert wird
    const config = CHALLENGE_CONFIG[challengeName];
    if (config && !this._data.challenges[challengeName]) {
      this._data.challenges[challengeName] = {
        started: false,
        completedTrainings: 0,
        totalTrainings: config.totalTrainings,
        completed: false
      };
    }

    this._data.challenges[challengeName] = {
      ...this._data.challenges[challengeName],
      ...data
    };

    // Prüfe automatisch, ob die Challenge durch diese Aktualisierung abgeschlossen wurde
    if (data.completedTrainings !== undefined) {
      const challengeData = this._data.challenges[challengeName];
      const completionPercentage = (challengeData.completedTrainings / challengeData.totalTrainings) * 100;
      const wasCompleted = challengeData.completed;
      challengeData.completed = completionPercentage >= 80;

      // Debug-Ausgabe für Challenge-Completion-Updates
      console.log(`Challenge '${challengeName}' Update:`, {
        completedTrainings: challengeData.completedTrainings,
        totalTrainings: challengeData.totalTrainings,
        completionPercentage: Math.round(completionPercentage),
        wasCompleted,
        isNowCompleted: challengeData.completed,
        statusChanged: wasCompleted !== challengeData.completed
      });
    }

    this._save();
  },

  /**
   * Initialisiert eine Challenge mit den korrekten Standardwerten
   * @param {string} challengeName - Name der Challenge
   */
  initializeChallenge(challengeName) {
    const config = CHALLENGE_CONFIG[challengeName];
    if (!config) {
      console.error(`Challenge-Konfiguration für '${challengeName}' nicht gefunden`);
      return;
    }

    if (!this._data.challenges[challengeName]) {
      this._data.challenges[challengeName] = {
        started: false,
        completedTrainings: 0,
        totalTrainings: config.totalTrainings,
        completed: false
      };
      this._save();
      console.log(`Challenge '${challengeName}' initialisiert mit ${config.totalTrainings} Trainings`);
    }
  },

  /**
   * Setzt das Healthgoal-Niveau (legacy - für Rückwärtskompatibilität)
   * @param {string} level - Das Niveau: "beginner", "intermediate", "advanced"
   */
  setHealthGoalLevel(level) {
    if (!['beginner', 'intermediate', 'advanced'].includes(level)) {
      console.error('Ungültiges Healthgoal-Niveau:', level);
      return;
    }
    this._data.healthGoalLevel = level;

    // Auch für das aktuelle Gesundheitsziel setzen, falls vorhanden
    if (this._data.currentHealthGoalContext) {
      this.setHealthGoalLevelForGoal(this._data.currentHealthGoalContext, level);
    }

    this._save();
    console.log('Healthgoal-Niveau gesetzt:', level);
  },

  /**
   * Setzt das Niveau für ein spezifisches Gesundheitsziel
   * @param {string} healthGoalKey - Der Schlüssel des Gesundheitsziels
   * @param {string} level - Das Niveau: "beginner", "intermediate", "advanced"
   */
  setHealthGoalLevelForGoal(healthGoalKey, level) {
    if (!['beginner', 'intermediate', 'advanced'].includes(level)) {
      console.error('Ungültiges Healthgoal-Niveau:', level);
      return;
    }
    if (!this._data.healthGoalLevels) {
      this._data.healthGoalLevels = {};
    }
    this._data.healthGoalLevels[healthGoalKey] = level;
    this._save();
    console.log(`Healthgoal-Niveau für ${healthGoalKey} gesetzt:`, level);
  },

  /**
   * Holt das Niveau für ein spezifisches Gesundheitsziel
   * @param {string} healthGoalKey - Der Schlüssel des Gesundheitsziels
   * @returns {string|null} Das Niveau oder null
   */
  getHealthGoalLevelForGoal(healthGoalKey) {
    if (!this._data.healthGoalLevels) {
      return null;
    }
    return this._data.healthGoalLevels[healthGoalKey] || null;
  },

  /**
   * Setzt den aktuellen Gesundheitsziel-Kontext
   * @param {string} healthGoalKey - Der Schlüssel des Gesundheitsziels
   */
  setCurrentHealthGoalContext(healthGoalKey) {
    this._data.currentHealthGoalContext = healthGoalKey;
    this._save();
    console.log('Aktueller Gesundheitsziel-Kontext gesetzt:', healthGoalKey);
  },

  /**
   * Holt den aktuellen Gesundheitsziel-Kontext
   * @returns {string|null} Der Schlüssel des aktuellen Gesundheitsziels oder null
   */
  getCurrentHealthGoalContext() {
    return this._data.currentHealthGoalContext;
  },

  /**
   * Prüft, ob aktuell eine Challenge aktiv ist (gestartet aber nicht abgeschlossen)
   * @returns {boolean} True wenn eine Challenge aktiv ist
   */
  hasActiveChallenge() {
    const challenges = this._data.challenges || {};
    return Object.keys(challenges).some(challengeKey => {
      const challengeData = challenges[challengeKey];
      return challengeData?.started && !challengeData?.completed;
    });
  },

  /**
   * Gibt den Schlüssel der aktuell aktiven Challenge zurück
   * @returns {string|null} Der Schlüssel der aktiven Challenge oder null
   */
  getActiveChallengeKey() {
    const challenges = this._data.challenges || {};
    const activeChallengeKey = Object.keys(challenges).find(challengeKey => {
      const challengeData = challenges[challengeKey];
      return challengeData?.started && !challengeData?.completed;
    });
    return activeChallengeKey || null;
  },

  /**
   * Prüft, ob eine spezifische Challenge aktuell aktiv ist
   * @param {string} challengeKey - Der Schlüssel der Challenge
   * @returns {boolean} True wenn die Challenge aktiv ist
   */
  isChallengeActive(challengeKey) {
    const challengeData = this._data.challenges?.[challengeKey];
    return challengeData?.started && !challengeData?.completed;
  },

  /**
   * Setzt eine Challenge zurück (für Reset-Funktionalität)
   * @param {string} challengeKey - Der Schlüssel der Challenge
   */
  resetChallenge(challengeKey) {
    const config = CHALLENGE_CONFIG[challengeKey];
    if (!config) {
      console.error(`Challenge-Konfiguration für '${challengeKey}' nicht gefunden`);
      return;
    }

    this._data.challenges[challengeKey] = {
      started: false,
      completedTrainings: 0,
      totalTrainings: config.totalTrainings,
      completed: false
    };
    this._save();
    console.log(`Challenge '${challengeKey}' zurückgesetzt`);
  },

  /**
   * Setzt ein Gesundheitsziel und alle zugehörigen Challenges zurück
   * @param {string} healthGoalKey - Der Schlüssel des Gesundheitsziels
   */
  resetHealthGoal(healthGoalKey) {
    const healthGoalConfig = HEALTH_GOAL_CONFIG[healthGoalKey];
    if (!healthGoalConfig) {
      console.error(`Gesundheitsziel-Konfiguration für '${healthGoalKey}' nicht gefunden`);
      return;
    }

    // Setze das Gesundheitsziel auf inaktiv
    this._data.activeHealthGoals[healthGoalKey] = false;

    // Setze das spezifische Niveau für dieses Gesundheitsziel zurück
    if (this._data.healthGoalLevels && this._data.healthGoalLevels[healthGoalKey]) {
      delete this._data.healthGoalLevels[healthGoalKey];
    }

    // Setze das globale Niveau nur zurück, wenn es das einzige aktive Gesundheitsziel war
    const hasOtherActiveGoals = Object.values(this._data.activeHealthGoals).some(isActive => isActive);
    if (!hasOtherActiveGoals) {
      this._data.healthGoalLevel = null;
    }

    // Setze alle zugehörigen Challenges zurück
    healthGoalConfig.availableChallenges.forEach(challengeKey => {
      this.resetChallenge(challengeKey);
    });

    this._save();
    console.log(`Gesundheitsziel '${healthGoalKey}' und alle zugehörigen Challenges zurückgesetzt`);
  },

  /**
   * Löscht alle gespeicherten Daten
   */
  reset() {
    this._data = {
      user: { name: null },
      connectedApps: {},
      challenges: {},
      activeHealthGoals: { fitUmgebung: false, aktivInDerNatur: false },
      healthGoalLevel: null,
      healthGoalLevels: {},
      currentHealthGoalContext: null
    };
    this._save();
    console.log('App-Daten zurückgesetzt');
  }
};

// Behalte appState für Abwärtskompatibilität
// export const appState = appStorage._data;

/**
 * Hilfsfunktion für den Zugriff auf appStorage._data
 * @returns {Object} Die gespeicherten App-Daten
 */
export const getAppData = () => appStorage._data;

/**
 * Challenge-Konfiguration mit Anzahl der Trainings pro Challenge
 * Diese Konfiguration definiert, wie viele Trainings jede Challenge hat
 */
export const CHALLENGE_CONFIG = {
  'lockereWanderung': {
    totalTrainings: 2,
    name: 'Lockere Wanderung'
  },
  'gassiGehen': {
    totalTrainings: 4, // 2 Mal pro Woche für 14 Tage
    name: 'Gassigehen mit Hund'
  },
  'fahrradTour': {
    totalTrainings: 3,
    name: 'Fahrrad Tour'
  },
  'spazierenGehen': {
    totalTrainings: 2,
    name: 'Spazieren gehen'
  },
  'plogging': {
    totalTrainings: 3,
    name: 'Plogging'
  },
  // Challenges für "Aktiv in der Natur"
  'ploggingActive': {
    totalTrainings: 4, // 2 Mal pro Woche für 14 Tage
    name: 'Plogging-Challenge'
  },
  'nordicWalking': {
    totalTrainings: 4,
    name: 'Nordic Walking'
  },
  'laufChallenge': {
    totalTrainings: 4,
    name: 'Lauf-Challenge'
  },
  'gassigehenTierheim': {
    totalTrainings: 4,
    name: 'Gassigehen mit (Tierheim-)Hund'
  },
  'inlineskating': {
    totalTrainings: 4,
    name: 'Inlineskating'
  },
  'intervallLaeufe': {
    totalTrainings: 4,
    name: 'Intervall-Läufe'
  },
  'abAufsRad': {
    totalTrainings: 4,
    name: 'Ab aufs Rad'
  }
};

/**
 * Gesundheitsziel-Konfiguration
 * Definiert, wie viele Challenges für jedes Gesundheitsziel abgeschlossen werden müssen
 */
export const HEALTH_GOAL_CONFIG = {
  'fitUmgebung': {
    name: 'Fit in deiner Umgebung',
    requiredChallenges: 4, // Von 5 verfügbaren Challenges müssen 4 abgeschlossen werden
    availableChallenges: ['lockereWanderung', 'gassiGehen', 'fahrradTour', 'spazierenGehen', 'plogging']
  },
  'aktivInDerNatur': {
    name: 'Aktiv in der Natur',
    requiredChallenges: 4, // Von 7 verfügbaren Challenges müssen 4 abgeschlossen werden
    availableChallenges: ['ploggingActive', 'nordicWalking', 'laufChallenge', 'gassigehenTierheim', 'inlineskating', 'intervallLaeufe', 'abAufsRad']
  }
};

/**
 * Berechnet die Anzahl der abgeschlossenen Challenges für ein spezifisches Gesundheitsziel
 * Eine Challenge gilt als abgeschlossen, wenn mindestens 80% der Trainings absolviert wurden
 * @param {string} healthGoalKey - Der Schlüssel des Gesundheitsziels (z.B. 'fitUmgebung')
 * @returns {Object} Objekt mit completedChallenges, totalChallenges und detailliertem Status
 */
export const calculateChallengeProgress = (healthGoalKey = 'fitUmgebung') => {
  const challenges = appStorage._data.challenges || {};
  const healthGoalConfig = HEALTH_GOAL_CONFIG[healthGoalKey];

  if (!healthGoalConfig) {
    console.error(`Gesundheitsziel-Konfiguration für '${healthGoalKey}' nicht gefunden`);
    return { completedChallenges: 0, totalChallenges: 0, completionPercentage: 0, challengeDetails: [] };
  }

  let completedChallenges = 0;
  const challengeDetails = [];

  // Durchlaufe alle Challenges des Gesundheitsziels
  healthGoalConfig.availableChallenges.forEach(challengeKey => {
    const challengeData = challenges[challengeKey];
    const config = CHALLENGE_CONFIG[challengeKey];

    if (!config) {
      console.warn(`Challenge-Konfiguration für '${challengeKey}' nicht gefunden`);
      return;
    }

    const completedTrainings = challengeData?.completedTrainings || 0;
    const totalTrainings = config.totalTrainings;
    const completionPercentage = totalTrainings > 0 ? (completedTrainings / totalTrainings) * 100 : 0;
    const isCompleted = challengeData?.started && completionPercentage >= 80;

    if (isCompleted) {
      completedChallenges++;
    }

    challengeDetails.push({
      key: challengeKey,
      name: config.name,
      started: challengeData?.started || false,
      completedTrainings,
      totalTrainings,
      completionPercentage: Math.round(completionPercentage),
      isCompleted,
      completedFlag: challengeData?.completed || false
    });
  });

  const totalChallenges = healthGoalConfig.requiredChallenges;
  const healthGoalCompletionPercentage = totalChallenges > 0 ? Math.round((completedChallenges / totalChallenges) * 100) : 0;

  return {
    completedChallenges,
    totalChallenges,
    completionPercentage: healthGoalCompletionPercentage,
    challengeDetails
  };
};

/**
 * Prüft, ob eine spezifische Challenge abgeschlossen ist (80%-Regel)
 * @param {string} challengeKey - Der Schlüssel der Challenge
 * @returns {boolean} True wenn die Challenge abgeschlossen ist
 */
export const isChallengeCompleted = (challengeKey) => {
  const challengeData = appStorage._data.challenges?.[challengeKey];
  const config = CHALLENGE_CONFIG[challengeKey];

  if (!challengeData || !config || !challengeData.started) {
    return false;
  }

  const completedTrainings = challengeData.completedTrainings || 0;
  const totalTrainings = config.totalTrainings;
  const completionPercentage = (completedTrainings / totalTrainings) * 100;

  return completionPercentage >= 80;
};

/**
 * Debug-Funktion: Zeigt den aktuellen Status aller Challenges und Gesundheitsziele an
 * Kann über die Browser-Konsole aufgerufen werden: window.debugHealthGoals()
 */
export const debugHealthGoals = () => {
  console.log('=== COMPLETE HEALTH GOAL DEBUG ===');
  console.log('AppStorage Data:', appStorage._data);

  Object.keys(HEALTH_GOAL_CONFIG).forEach(healthGoalKey => {
    const progress = calculateChallengeProgress(healthGoalKey);
    console.log(`\nHealth Goal: ${HEALTH_GOAL_CONFIG[healthGoalKey].name}`);
    console.log('Progress:', progress);
  });

  console.log('\nAll Challenges:');
  Object.keys(CHALLENGE_CONFIG).forEach(challengeKey => {
    const challengeData = appStorage._data.challenges?.[challengeKey];
    const config = CHALLENGE_CONFIG[challengeKey];
    console.log(`${config.name}:`, challengeData || 'Not initialized');
  });

  console.log('=== END COMPLETE DEBUG ===');
};
