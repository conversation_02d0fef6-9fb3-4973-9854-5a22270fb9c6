import { html, render } from "lit-html";

let currentTime = new Date().toLocaleTimeString([], {
  hour: "2-digit",
  minute: "2-digit",
});

// Funktion, die das Template erstellt
const clockTemplate = () => html` <p>${currentTime}</p> `;

// Update-Funktion
function updateClock() {
  currentTime = new Date().toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
  }); // Ohne <PERSON>
  render(clockTemplate(), document.querySelector("#app-time")); // Template neu rendern
}

// Initiales Rendern
render(clockTemplate(), document.querySelector("#app-time"));

// Uhrzeit jede Minute aktualisieren
setInterval(updateClock, 60000);
