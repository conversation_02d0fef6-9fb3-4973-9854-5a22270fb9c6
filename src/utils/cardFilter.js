/**
 * Erstellt eine wiederverwendbare Filterfunktion für Karten basierend auf Filter-Chips
 * 
 * @param {string} containerSelector - CSS-Selektor für den Container mit den Karten
 * @param {string} cardSelector - CSS-Selektor für die einzelnen Karten
 * @param {string} categoryAttribute - Name des Attributs, das die Kategorie enthält
 * @returns {Function} - Filterfunktion, die mit den aktiven Filtern aufgerufen werden kann
 */
export function createCardFilter(containerSelector, cardSelector, categoryAttribute = 'data-category') {
  return function filterCards(activeFilters) {
    const container = document.querySelector(containerSelector);
    if (!container) return;
    
    const cards = container.querySelectorAll(cardSelector);
    
    // Wenn keine Filter aktiv sind, alle Karten anzeigen
    if (!activeFilters || activeFilters.length === 0) {
      cards.forEach(card => {
        // Wenn die Karte ausgeblendet ist, mit Animation einblenden
        if (card.style.display === 'none') {
          card.style.display = '';
          card.classList.remove('fade-out');
          card.classList.add('fade-in');
          
          // Animation-Klasse nach Abschluss entfernen
          card.addEventListener('animationend', () => {
            card.classList.remove('fade-in');
          }, { once: true });
        }
      });
      return;
    }
    
    // Karten filtern basierend auf den aktiven Filtern
    cards.forEach(card => {
      const category = card.getAttribute(categoryAttribute);
      const shouldShow = activeFilters.includes(category);
      
      if (shouldShow && card.style.display === 'none') {
        // Karte mit Animation einblenden
        card.classList.remove('fade-out');
        card.classList.add('fade-in');
        card.style.display = '';
        
        // Animation-Klasse nach Abschluss entfernen
        card.addEventListener('animationend', () => {
          card.classList.remove('fade-in');
        }, { once: true });
      } 
      else if (!shouldShow && card.style.display !== 'none') {
        // Karte mit Animation ausblenden
        card.classList.remove('fade-in');
        card.classList.add('fade-out');
        
        // Nach Abschluss der Animation ausblenden
        card.addEventListener('animationend', () => {
          card.style.display = 'none';
          card.classList.remove('fade-out');
        }, { once: true });
      }
    });
  };
}

/**
 * Fügt Kategorie-Attribute zu Karten hinzu
 * 
 * @param {string} containerSelector - CSS-Selektor für den Container mit den Karten
 * @param {string} cardSelector - CSS-Selektor für die einzelnen Karten
 * @param {Array<string>} categories - Array mit Kategorien in der Reihenfolge der Karten
 * @param {string} categoryAttribute - Name des Attributs, das die Kategorie enthält
 */
export function addCategoryAttributes(containerSelector, cardSelector, categories, categoryAttribute = 'data-category') {
  setTimeout(() => {
    const container = document.querySelector(containerSelector);
    if (!container) return;
    
    const cards = container.querySelectorAll(cardSelector);
    cards.forEach((card, index) => {
      if (index < categories.length) {
        card.setAttribute(categoryAttribute, categories[index]);
      }
    });
  }, 0);
}
