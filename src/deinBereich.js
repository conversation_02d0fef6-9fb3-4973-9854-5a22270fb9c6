import { html } from "lit-html";
import { BottomNavigation } from "./bottomNavigation.js";
import { createSegmentedControl } from "../webcomponents/segmentedControl.js";
import { deinBereich_segment1Content } from "../webcomponents/segments/deinBereich_Segment1.js";
import { deinBereich_segment2Content } from "../webcomponents/segments/deinBereich_Segment2.js";
import { deinBereich_segment3Content } from "../webcomponents/segments/deinBereich_Segment3.js";

/* Segmented Control */
const segments = [
  {
    id: "Segment1",
    title: "Aktuelles",
    content: deinBereich_segment1Content,
  },
  {
    id: "Segment2",
    title: "Historie",
    content: deinBereich_segment2Content,
  },
  {
    id: "Segment3",
    title: "Favoriten",
    content: deinBereich_segment3Content,
  },
];

export const segmentedControl = createSegmentedControl(segments);

/**
 * Template für die "Dein Bereich" Seite
 * @returns {TemplateResult} Das Seiten-Template
 */
export const deinBereichTemplate = () => {
  return html`
  <div class="features-headline">
    <div class="page-headline-container" style="background-color: var(--primary-brand);">
      <h1>Dein <span class="light-grn-text">Bereich</span></h1>
    </div>
  </div>
    <div id="segmentedControlContainer" class="standard-container">
      ${segmentedControl.template}
    </div>
    ${BottomNavigation("dein-bereich")}
  `;
};

/**
 * Initialisiert die Segmented Control nach dem Rendern
 */
export const initializeDeinBereich = () => {
  setTimeout(() => {
    segmentedControl.initialize();
  }, 0);
};
