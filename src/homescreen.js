import { html } from "lit-html";
import iconFeatures from "../svg/icons/Features.svg";
import iconMagazine from "../svg/icons/Magazine.svg";
import iconTraining from "../svg/icons/Training.svg";
import { infoCardSlider } from "../webcomponents/infoCardSlider.js";
import { illustrationCard } from "../webcomponents/illustrationCard.js";
import compositionWomanChild from "../svg/CompositionWomanChild.svg";
import compositionHikingKomoot from "../svg/CompositionHikingKomoot.svg";
import img_peopleHugging from "../img/people_hugging.jpg";
import img_cyberFitness from "../img/cyberfitness.jpg";
import { feedCard } from "../webcomponents/feedCard.js";
import { BottomNavigation } from "./bottomNavigation.js";
import { appStorage } from "../utils.js";

/* Single Template that will combined at the bottom */
export const templateHomescreen01 = () => html`
  <div class="green-bg-box content-padding top-bg-box content-left-align">
    <h1>Hallo <span class="light-grn-text">${appStorage.userName}!</span></h1>
    <h3>Schön, dass Du da bist!</h3>
    ${infoCardSlider({
      icoPath: iconFeatures,
      title: "Deine Gesundheitsassistentin",
      text: "Lerne die Funktionen<br />von AOK NAVIDA kennen",
      linkText: "",
    })}
  </div>
  <div
    class="standard-container content-padding content-left-align green-headline"
  >
    <h3>Wirf einen Blick rein</h3>
  </div>
`;

/* Single Template that will combined at the bottom */
export const templateIlluCardHealthgoals = () => html`
  <div
    class="standard-container last-container content-padding content-left-align gap-items-24"
  >
    ${illustrationCard({
      bgColor: "secondary-green",
      svgPath: compositionWomanChild,
      title: "Fitter werden und Bonuspunkte sammeln",
      text: "Mit den Gesundheitszielen zu Sport und Bewegung",
      showButton: true,
      buttonText: "Zu den Bewegungszielen",
      link: "/healthgoals-overview-no-goal",
    })}
    ${feedCard({
      imgPath: img_peopleHugging,
      icoPath: iconMagazine,
      categoryName: "Magazin",
      title: "Psychische Gesundheit: Was schadet, was hilft ihr?",
      text: "Menschen können im Leben mit Problemen und Krisen konfrontiert werden, die sehr belastend sind und das Wohlbefinden beeinträchtigen können.",
    })}
    ${feedCard({
      imgPath: img_cyberFitness,
      icoPath: iconTraining,
      categoryName: "Gesundheitskurs",
      title: "Onlinepräventionskurs Pilates",
      text: "CyberConcept GmbH",
    })}
    ${illustrationCard({
      bgColor: "secondary-green",
      svgPath: compositionHikingKomoot,
      title: "Umgebung entdecken und Bonuspunkte sammeln",
      text: "Entdecke tolle Outdoor-Routen meines Partners komoot und sammle dabei gleichzeitig Bonuspunkte.",
      showButton: true,
      buttonText: "komoot-Ziel entdecken",
    })}
  </div>
`;

/* Combining single Templates */
export const homescreenTemplate = () => html`
  ${templateHomescreen01()} ${templateIlluCardHealthgoals()}
  ${BottomNavigation("home")}
`;

console.log("homescreen.js loaded");
