import { html } from "lit-html";
import { BottomNavigation } from "./bottomNavigation.js";
import { listItem } from "../webcomponents/listItem.js";
import { infoCardwButton } from "../webcomponents/infoCardwButton.js";
import { buttonTextIcon } from "../webcomponents/buttonTextIcon.js";
import iconChevronRight from "../svg/icons/icon_chevron_right.svg";
import iconLogout from "../svg/icons/icon_logout.svg";
import iconLinkExternal from "../svg/icons/icon_linkExternal.svg";

/**
 * Template für die Einstellungen-Seite
 * @returns {TemplateResult} Das Seiten-Template
 */
export const settingsTemplate = () => {
  // Erste Liste mit Einstellungen bis Biometrie
  const settingsListPart1 = [
    {
      text: "Push-Benachrichtigungen",
      icon: iconChevronRight,
      iconPosition: "right",
      onClick: () => console.log("Push-Benachrichtigungen clicked")
    },
    {
      text: "Einwilligungen",
      icon: iconChevronRight,
      iconPosition: "right",
      onClick: () => console.log("Einwilligungen clicked")
    },
    {
      text: "Biometrie",
      icon: iconChevronRight,
      iconPosition: "right",
      onClick: () => console.log("Biometrie clicked")
    }
  ];

  // Zweite Liste mit Einstellungen ab Hilfe & Support
  const settingsListPart2 = [
    {
      text: "Hilfe & Support",
      icon: iconChevronRight,
      iconPosition: "right",
      onClick: () => console.log("Hilfe & Support clicked")
    },
    {
      text: "Nutzungsbedingungen",
      icon: iconChevronRight,
      iconPosition: "right",
      onClick: () => console.log("Nutzungsbedingungen clicked")
    },
    {
      text: "Datenschutz",
      icon: iconChevronRight,
      iconPosition: "right",
      onClick: () => console.log("Datenschutz clicked")
    },
    {
      text: "Barrierefreiheit",
      icon: iconChevronRight,
      iconPosition: "right",
      onClick: () => console.log("Barrierefreiheit clicked")
    },
    {
      text: "Dein Profil",
      icon: iconChevronRight,
      iconPosition: "right",
      onClick: () => console.log("Dein Profil clicked")
    },
    {
      text: "Deine NAVIDA-Daten",
      icon: iconChevronRight,
      iconPosition: "right",
      onClick: () => console.log("Deine NAVIDA-Daten clicked")
    }
  ];

  return html`
    <div class="page-content">
      <div class="features-headline">
        <div class="page-headline-container" style="background-color: var(--primary-brand);">
          <h1>Einstellungen</h1>
        </div>
      </div>

      <div class="standard-container flex-full-width">
        <div class="content-padding">
          ${listItem(settingsListPart1)}
        </div>

        <div class="content-padding">
          ${infoCardwButton({
            text: "Hilf uns in nur wenigen Minuten, die App für Dich zu verbessern.",
            linkText: "Jetzt Feedback geben",
            background: "--info-card-background-blue",
            linkIcon: iconLinkExternal,
            linkTextAlign: "left"
          })}
        </div>

        <div class="content-padding">
          ${listItem(settingsListPart2)}
        </div>
        <div class="divider full-width"></div>
        <div class="content-padding last-container content-top-padding">
          ${buttonTextIcon("Abmelden", iconLogout, "left")}
        </div>
      </div>
    </div>

    ${BottomNavigation("settings")}
  `;
};
