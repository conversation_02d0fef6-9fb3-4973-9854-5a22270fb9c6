import { html } from "lit-html";
import { buttonTextIcon } from "../../../webcomponents/buttonTextIcon.js";
import { router, exitWithSlide } from "../../../router.js";
import iconMail from "../../../svg/icons/icon_mail.svg";
import logoKomootBreit from "../../../img/logos/logo_komootbreit.png";
import { appStorage } from "../../../utils.js";

/**
 * Komoot Login Template
 * @returns {TemplateResult} Das Komoot Login Template
 */
export const komootLoginTemplate = () => {
  return html`
    <div class="standard-container content-padding content-top-padding">
      <!-- Logo zentriert -->
      <div style="display: flex; justify-content: center; margin-bottom: 32px;">
        <img src="${logoKomootBreit}" alt="Komoot Logo" style="max-width: 200px;" />
      </div>

      <!-- Anmelde-Text -->
      <p style="text-align: center; margin-bottom: 32px;">
        Melde Dich bei Deinem komott-Konto an oder erstelle ein neues.
      </p>

      <!-- E-Mail Eingabe -->
      <p style="font-weight: bold; margin-bottom: 8px;">
        Gib deine E-Mail ein
      </p>

      <!-- Input Feld -->
      <input
        type="email"
        placeholder="<EMAIL>"
        value="<EMAIL>"
        style="margin-bottom: 24px;"
      />

      <!-- Anmelde-Button -->
      <div @click=${() => {
        // Komoot als verbunden markieren
        if (!appStorage.connectedApps) {
          appStorage._data.connectedApps = {};
        }
        appStorage.setAppConnection('komoot', true);
        console.log("Komoot verbunden");
        
        // Auch das Gesundheitsziel aktivieren
        appStorage.activateHealthGoal("fitUmgebung");
        console.log("Gesundheitsziel 'Fit in deiner Umgebung' aktiviert");

        // Zurück zur Tracker-Connect-Seite
        router.navigate("/tracker-connect");
      }}>
        ${buttonTextIcon("Mit E-Mail anmelden", iconMail, "left")}
      </div>
    </div>
  `;
};
