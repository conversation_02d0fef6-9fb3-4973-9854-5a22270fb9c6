import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconSmartphoneTracker from "../../../svg/icons/icon_smartphoneTracker.svg";
import { router, exitWithSlide } from "../../../router.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { sectionSubtitle } from "../../../webcomponents/sectionSubtitle.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { sectionParagraph } from "../../../webcomponents/sectionParagraph.js";
import { sectionBulletList } from "../../../webcomponents/sectionBulletList.js";
import { titleDuoColor } from "../../../webcomponents/titleDuoColor.js";
import imgPlogging from "../../../img/healthgoals/hg_plogging.jpg";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { appStorage } from "../../../utils.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Zurück zur Challenge-Seite
  const backTarget = "/challenge/plogging"; 
  
  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget)
    })}
  `;
};

/**
 * Template für die "Joggen und Müll sammeln (Plogging)" Trainings-Seite
 * @returns {TemplateResult} Das Trainings-Seiten Template
 */
export const templatePlogging = () => {
  // Prüfen, ob ein aktives Gesundheitsziel vorhanden ist
  const hasActiveHealthGoal = appStorage._data.activeHealthGoals &&
    (appStorage._data.activeHealthGoals.fitUmgebung ||
     Object.values(appStorage._data.activeHealthGoals).some(value => value === true));
  
  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <div class="content-padding center-text content-bottom-padding">
        ${titleDuoColor("Joggen und Müll sammeln (Plogging)", "", "h1")}
        ${sectionSubtitle("6 bis 8 km", "dark-grn-text", true)}        
        <div class="standard-container">
          ${infoCardPlain({
            icon: iconSmartphoneTracker,
            text: "Bitte starte eine Aktivität auf Deinem Fitness-Armband oder in Deiner Fitness-App, falls es das nicht automatisch macht.",
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
      </div>
    </div>
    <img src="${imgPlogging}" alt="Training Joggen und Müll sammeln (Plogging)">
    <div class="content-padding standard-container content-top-padding">
      ${sectionSubtitle("Details zur Übung", "dark-grn-text")}
    </div>
      ${sectionParagraph(
        "Entdecke Deine Umgebung beim Joggen und tue gleichzeitig etwas Gutes für die Umwelt! Beim Joggen Müll aufzusammeln ist nicht nur ein Beitrag zu einer sauberen Umwelt, sondern auch eine gute Möglichkeit, Deine Fitness zu verbessern."
      )}
      ${sectionParagraph(
        "Du brauchst nur eine Mülltüte und Handschuhe und schon kann es losgehen. Die Kombination aus leichtem Ausdauertraining und der Bewegungsabfolge beim Aufsammeln – in die Knie gehen, Vorbeugen und Aufrichten – stärkt nicht nur Dein Herz-Kreislauf-System, sondern auch Deine Rumpf-, Bein- und Armmuskulatur. So wird das Laufen zum Ganzkörpertraining."
      )}
      ${sectionParagraph(
        "Ob im Park, im Wald oder in der Stadt – mache Deine nächste Laufrunde zur Müllsammelaktion und Deine Umgebung ein Stück sauberer. Deine Gesundheit und die Umwelt werden es Dir danken!"
      )}

      ${sectionParagraph("Vorbereitung und Planung", true)}
      ${sectionBulletList([
        "Wähle eine Route, die zu Deiner Fitness passt",
        "Plane 45-60 Minuten für Dein Plogging ein",
        "Informiere Dich über Müllentsorgungsmöglichkeiten auf Deiner Route",
        "Überprüfe das Wetter und wähle passende Kleidung",
        "Nimm eine Wasserflasche mit"
      ])}

      ${sectionParagraph("Ausrüstung", true)}
      ${sectionBulletList([
        "Bequeme Laufkleidung je nach Jahreszeit",
        "Gut passende Laufschuhe mit ausreichend Dämpfung",
        "Handschuhe zum Schutz beim Müllsammeln",
        "Müllbeutel oder -sack",
        "Wasser zum Trinken",
        "Optional: Greifzange für schwer erreichbaren Müll"
      ])}

      ${sectionParagraph("Tipps für das Plogging", true)}
      ${sectionBulletList([
        "Beginne mit einem leichten Aufwärmen",
        "Wechsle zwischen Joggen und Gehen, je nach Deiner Kondition",
        "Achte auf eine korrekte Körperhaltung beim Bücken (Knie beugen, Rücken gerade)",
        "Sammle nur Müll ein, den Du sicher anfassen kannst (keine Glasscherben, Spritzen etc.)",
        "Mache regelmäßige Trinkpausen",
        "Entsorge den gesammelten Müll ordnungsgemäß"
      ])}

    <div class="content-padding standard-container content-bottom-padding content-top-padding">
        ${infoCardPlain({
          title: "Vorsichtsmaßnahmen",
          text: unsafeHTML(`In den folgenden Fällen solltest Du bitte keine Plogging-Übung machen:
          <ul>
            <li>Wenn Du eine Verletzung an Beinen, Rücken oder Armen hast.</li>
            <li>Wenn Du bei der Übung Schmerzen verspürst.</li>
            <li>Bei extremen Wetterbedingungen wie Gewitter oder Glatteis.</li>
            <li>Bei Herz-Kreislauf-Erkrankungen ohne ärztliche Freigabe.</li>
          </ul>
          <p><strong>Hinweise:</strong> Trage immer Handschuhe zum Schutz. Sammle keinen gefährlichen Müll wie Glasscherben, Spritzen oder scharfkantige Gegenstände auf. Achte auf ausreichend Flüssigkeitszufuhr.</p>`),
          textColor: "white",
          backgroundColor: "var(--info-card-background-red)"
        })}
    </div>
  `;
};

console.log("Training Joggen und Müll sammeln (Plogging) loaded");
