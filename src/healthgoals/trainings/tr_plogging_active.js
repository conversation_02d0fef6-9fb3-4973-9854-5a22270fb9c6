import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconSmartphoneTracker from "../../../svg/icons/icon_smartphoneTracker.svg";
import iconIlluTrackerHeart from "../../../svg/icons/icon_illu_trackerheart.svg";
import { router, exitWithSlide } from "../../../router.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { sectionSubtitle } from "../../../webcomponents/sectionSubtitle.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { sectionParagraph } from "../../../webcomponents/sectionParagraph.js";
import { sectionBulletList } from "../../../webcomponents/sectionBulletList.js";
import { titleDuoColor } from "../../../webcomponents/titleDuoColor.js";
import imgPloggingActive from "../../../img/healthgoals/challenges/ch_plogging_active.jpg";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { appStorage } from "../../../utils.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Zurück zur Challenge-Seite
  const backTarget = "/challenge/plogging-active"; 
  
  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget)
    })}
  `;
};

/**
 * Template für die "30 Min. Normales Gehen/Spazieren" Trainings-Seite
 * @returns {TemplateResult} Das Trainings-Seiten Template
 */
export const templatePloggingActive = () => {
  // Prüfen, ob ein aktives Gesundheitsziel vorhanden ist
  const hasActiveHealthGoal = appStorage._data.activeHealthGoals &&
    (appStorage._data.activeHealthGoals.aktivInDerNatur ||
     Object.values(appStorage._data.activeHealthGoals).some(value => value === true));
  
  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <div class="content-padding center-text content-bottom-padding">
        ${titleDuoColor("30 Min. Normales Gehen/Spazieren", "", "h1")}
        ${sectionSubtitle("(zur Orientierung 3,5 km/h)", "dark-grn-text", true)}        
        <div class="standard-container">
          ${infoCardPlain({
            icon: iconIlluTrackerHeart,
            text: "Bitte starte eine Aktivität auf Deinem Fitness-Armband oder in Deiner Fitness-App, falls es das nicht automatisch macht.",
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
        <style>
          .info-card-plain .icon-container img {
            filter: brightness(0) saturate(100%) invert(26%) sepia(86%) saturate(608%) hue-rotate(116deg) brightness(90%) contrast(104%);
          }
        </style>
      </div>
    </div>
    <img src="${imgPloggingActive}" alt="Training 30 Min. Normales Gehen/Spazieren">
    <div class="content-padding standard-container content-top-padding">
      ${sectionSubtitle("Details zur Übung", "dark-grn-text")}
    </div>
      ${sectionParagraph(
        "Du favorisierst eher entspanntes Laufen und möchtest gleichzeitig der Umwelt etwas Gutes tun? Beim \"Plogging\" wird die Laufrunde zum Müllsammeln genutzt."
      )}
      ${sectionParagraph(
        "Also: Schnür die Laufschuhe, bewaffne Dich mit Müllbeutel und idealerweise mit Handschuhen und los geht´s!"
      )}
      ${sectionParagraph(
        "Übrigens: Der Begriff \"Plogging\" wird abgeleitet vom schwedischen \"Plogga\". Dieses wiederum ist eine Mischung aus \"Plocka\" (=\"Aufsammeln\") und \"Jogga\" (=\"Joggen\"). Der Trend hat mittlerweile auch andere Länder erreicht. Durch die Bewegungsabfolge Joggen - in die Knie gehen - Vorbeugen - Aufrichten - Joggen werden gleich mehrere Muskelgruppen aktiviert."
      )}

      ${sectionParagraph("Vorbereitung und Planung", true)}
      ${sectionBulletList([
        "Wähle eine Route, die zu Deiner Fitness passt (ca. 3,5 km/h Gehgeschwindigkeit)",
        "Plane 30 Minuten für Dein Training ein",
        "Informiere Dich über Müllentsorgungsmöglichkeiten auf Deiner Route",
        "Überprüfe das Wetter und wähle passende Kleidung",
        "Nimm eine Wasserflasche mit"
      ])}

      ${sectionParagraph("Ausrüstung", true)}
      ${sectionBulletList([
        "Bequeme Gehkleidung je nach Jahreszeit",
        "Gut passende Schuhe mit ausreichend Dämpfung",
        "Handschuhe zum Schutz beim Müllsammeln",
        "Müllbeutel oder -sack",
        "Wasser zum Trinken",
        "Optional: Greifzange für schwer erreichbaren Müll"
      ])}

      ${sectionParagraph("Tipps für das Training", true)}
      ${sectionBulletList([
        "Beginne mit einem leichten Aufwärmen",
        "Halte eine entspannte Gehgeschwindigkeit von ca. 3,5 km/h",
        "Achte auf eine korrekte Körperhaltung beim Bücken (Knie beugen, Rücken gerade)",
        "Sammle nur Müll ein, den Du sicher anfassen kannst (keine Glasscherben, Spritzen etc.)",
        "Mache regelmäßige Trinkpausen",
        "Entsorge den gesammelten Müll ordnungsgemäß"
      ])}

    <div class="content-padding standard-container content-bottom-padding content-top-padding">
        ${infoCardPlain({
          title: "Vorsichtsmaßnahmen",
          text: unsafeHTML(`Du solltest die Challenge nicht weiter ausführen, wenn während der Übung akute Schmerzen in den Gelenken oder Muskeln auftreten.
          <br><br>
          Darüber hinaus darf die Challenge in folgenden Fällen nicht ausgeführt werden:
          <ul>
            <li>bei akuten Erkrankungen</li>
            <li>nach operativen Eingriffen</li>
            <li>Bei chronischen Erkrankungen halte bitte Rücksprache mit Deinem Arzt/Deiner Ärztin.</li>
          </ul>
          <p><strong>Hinweise:</strong> Schwangere, sowie Menschen die kürzlich entbunden haben, sollten die Challenge nicht durchführen. Bitte halte auch in diesem Fall Rücksprache mit Deinem Arzt/Deiner Ärztin.</p>`),
          textColor: "white",
          backgroundColor: "var(--info-card-background-red)"
        })}
    </div>
  `;
};

console.log("Training 30 Min. Normales Gehen/Spazieren (Plogging-Active) loaded");
