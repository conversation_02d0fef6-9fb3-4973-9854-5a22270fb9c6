import { html } from "lit-html";
import { titleDuoColor } from "../../webcomponents/titleDuoColor.js";
import { sectionParagraph } from "../../webcomponents/sectionParagraph.js";
import { topMenu } from "../../webcomponents/topMenu.js";
import iconArrowLeft from "../../svg/icons/ArrowLeft.svg";
import iconClose from "../../svg/icons/icon_close.svg";
import { infoCardPlain } from "../../webcomponents/infoCardPlain.js";
import { sectionTitle } from "../../webcomponents/sectionTitle.js";
import { healthgoalCardActive } from "../../webcomponents/healthgoalCardActive.js";
import { buttonStandard } from "../../webcomponents/buttonStandard.js";
import iconMoneypig from "../../svg/icons/icon_moneypig.svg";
import iconTimelater from "../../svg/icons/icon_timelater.svg";
import imgKomoot from "../../img/healthgoals/komoot_hg.jpg";
import { router } from "../../router.js";

/* Top Menu */
const templateTopMenu = () => html`
  ${topMenu({
    backIcon: iconArrowLeft,
    primaryIcon: "",
    menuIcon: "",
  })}
`;

export const templateNiveauInitial = () => {
  return html`
    <!-- Top Menu -->
    ${templateTopMenu()}
    <!-- Content -->
    <div class="content-padding standard-container">
      ${titleDuoColor("Dein individuelles", "Leistungsniveau")}
    </div>
    ${sectionParagraph(
      "Gleich kann’s losgehen! Damit Du Dein Ziel auch in Deinem Tempo erreichen kannst, müssen wir nur noch gemeinsam Dein individuelles Leistungsniveau bestimmen. Keine Sorge, das dauert nur wenige Minuten. Ich werde Dich hierfür bitten, ein paar Fragen zu beantworten und kurze Übungen zu absolvieren. Ich passe dann alle Challenges zu diesem Gesundheitsziel genau an Dich an. "
    )}
    <div class="standard-container content-padding content-bottom-padding">
      ${infoCardPlain({
        title: "Hinweis",
        text: "Bitte führe die folgenden Schritte nur durch, wenn Du Dich aktuell gesund fühlst und keine akuten Schmerzen oder Verletzungen vorliegen.  ",
        textColor: "white",
        backgroundColor: "var(--info-card-background-red)",
      })}
    </div>
    ${sectionTitle("Dein Gesundheitsziel")}
    <div class="standard-container content-padding content-bottom-padding">
      ${healthgoalCardActive({
        cardImage: imgKomoot,
        tagCategory: "",
        tagIcon: "",
        tagColor: "",
        tagTextColor: "",
        healthgoalTitle: "Fit in deiner Umgebung",
        healthgoalCoop: "In Kooperation mit komoot",
        pillText: "",
        pillColor: "",
        goalinfoIcon1: iconMoneypig,
        goalinfoText1: "1500 Bonuspunkte",
        goalinfoIcon2: iconTimelater,
        goalinfoText2: "max. 90 Tage",
        completedChallenges: 0,
        totalChallenges: 0,
        elevated: true,
      })}
    </div>
    <div class="standard-container content-padding">
      ${buttonStandard({
        text: "Leistungsniveau bestimmen",
        variant: "primary",
        link: "/hg-niveau-poll",
      })}
    </div>
  `;
};
