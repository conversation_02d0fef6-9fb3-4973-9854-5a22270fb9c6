import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconInfo from "../../../svg/icons/icon_info.svg";
import iconTimelater from "../../../svg/icons/icon_timelater.svg";
import { exitWithSlide, router } from "../../../router.js";
import { buttonTextIcon } from "../../../webcomponents/buttonTextIcon.js";
import { setupChallengeResetMenu } from "../../../helpers/reset-helper.js";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { infoCardBullets } from "../../../webcomponents/infoCardBullets.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { createSegmentedControl } from "../../../webcomponents/segmentedControl.js";
import { pillsContainer } from "../../../webcomponents/pills.js";
import iconChevronRight from "../../../svg/icons/icon_chevron_right.svg";
import { listItem } from "../../../webcomponents/listItem.js";
import { appStorage, getAppData } from "../../../utils.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { imageCardNarrow } from "../../../webcomponents/imageCardNarrow.js";
import imgPloggingActive from "../../../img/healthgoals/challenges/ch_plogging_active.jpg";
import { initDragScroll, refreshDragScroll } from "../../../helpers/dragScroll.js";
import { showDialog } from "../../../helpers/dialog-helper.js";
import iconLink from "../../../svg/icons/icon_link.svg";
import iconTagSport from "../../../svg/icons/icon_tag_sport.svg";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Bestimme das Zurück-Ziel basierend auf dem aktiven Status des Gesundheitsziels
  const backTarget = appStorage._data.activeHealthGoals.aktivInDerNatur
    ? "/healthgoals-overview/hg-aktivInDerNatur-active"
    : "/healthgoals-overview/hg-aktivInDerNatur";

  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget),
      menuId: "challenge-plogging-active-menu"
    })}
  `;
};

/**
 * List Item Content für Trainingsplan
 */
const trainingsplan = [
  {
    text: "1. Training: 30 Min. Normales Gehen/Spazieren",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/plogging-active")
  },
  {
    text: "2. Training: 30 Min. Normales Gehen/Spazieren",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/plogging-active")
  },
  {
    text: "3. Training: 30 Min. Normales Gehen/Spazieren",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/plogging-active")
  },
  {
    text: "4. Training: 30 Min. Normales Gehen/Spazieren",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/plogging-active")
  }
];

/**
 * Segmented Control für die Tabs "Übung" und "Trainingsplan"
 */
const segments = [
  {
    id: "uebung",
    title: "Übung",
    content: html`
      <div class="tabpadding black-text">
        <!-- Beschreibungstext -->
        <p class="challenge-description content-padding">
          Du favorisierst eher entspanntes Laufen und möchtest gleichzeitig der Umwelt etwas Gutes tun? Beim "Plogging" wird die Laufrunde zum Müllsammeln genutzt.
        </p>

        <!-- Grüne Info-Card mit Bullet Points -->
        <div class="standard-container content-padding">
          ${infoCardBullets({
            title: "Was wirst Du erreichen?",
            bulletPoints: [
              "Durch die Aktivität an der frischen Luft wird Dein Herz-Kreislaufsystem gestärkt.",
              "Deine Fettverbrennung wird angekurbelt.",
              "Du tust der Umwelt etwas Gutes – das sorgt nebenbei für die Ausschüttung von Glückshormonen."
            ],
            background: "--info-card-background-green"
          })}
        </div>

        ${sectionTitle("Diese Übungen erwarten Dich")}
        <!-- Image Cards -->
        <div class="image-card-narrow-scroll-container">
          <div class="standard-container content-padding image-card-narrow-container">
            ${imageCardNarrow({
              imgPath: imgPloggingActive,
              title: "30 Min. Normales Gehen/Spazieren",
              altText: "30 Min. Normales Gehen/Spazieren",
              link: "/training/plogging-active"
            })}
            ${imageCardNarrow({
              imgPath: imgPloggingActive,
              title: "30 Min. Normales Gehen/Spazieren",
              altText: "30 Min. Normales Gehen/Spazieren",
              link: "/training/plogging-active"
            })}
            ${imageCardNarrow({
              imgPath: imgPloggingActive,
              title: "30 Min. Normales Gehen/Spazieren",
              altText: "30 Min. Normales Gehen/Spazieren",
              link: "/training/plogging-active"
            })}
            ${imageCardNarrow({
              imgPath: imgPloggingActive,
              title: "30 Min. Normales Gehen/Spazieren",
              altText: "30 Min. Normales Gehen/Spazieren",
              link: "/training/plogging-active"
            })}
          </div>
        </div>

        <!-- Gelbe Info-Card mit Vorsichtsmaßnahmen -->
        <div class="standard-container content-padding">
          ${infoCardPlain({
            title: "Vorsichtsmaßnahmen",
            text: unsafeHTML(`Du solltest die Challenge nicht weiter ausführen, wenn während der Übung akute Schmerzen in den Gelenken oder Muskeln auftreten.
            <br><br>
            Darüber hinaus darf die Challenge in folgenden Fällen nicht ausgeführt werden:
            <ul>
              <li>bei akuten Erkrankungen</li>
              <li>nach operativen Eingriffen</li>
              <li>Bei chronischen Erkrankungen halte bitte Rücksprache mit Deinem Arzt/Deiner Ärztin.</li>
            </ul>
            <p><strong>Hinweise:</strong> Schwangere, sowie Menschen die kürzlich entbunden haben, sollten die Challenge nicht durchführen. Bitte halte auch in diesem Fall Rücksprache mit Deinem Arzt/Deiner Ärztin.</p>`),
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
      </div>
    `
  },
  {
    id: "trainingsplan",
    title: "Trainingsplan",
    content: html`
      <div class="tabpadding">
        <div class="content-padding content-no-top-padding">
          ${listItem(trainingsplan)}
        </div>
      </div>
    `
  }
];

const segmentedControl = createSegmentedControl(segments);

/**
 * Zeigt den Dialog zur App-Verbindung an
 */
const showConnectionDialog = () => {
  showDialog({
    title: "Verbinde bitte Deine Fitness-App",
    text: unsafeHTML("<p>Wähle mindestens eine der folgenden Verbindungen aus, bevor Du mit dieser Challenge startest.</p> <p>Verbinde Dein Fitness-Armband und starte eine Aktivität.</p> <p>Für die Bonifizierung prüft die NAVIDA-App Deine Trainingsdaten sobald Du den Button \"Training erfassen\" drückst.</p>"),
    icon: iconLink,
    iconColor: "var(--bubble-yellow)",
    iconCorner: "top-left",
    buttonPrimary: {
      text: "Weiter",
      link: "/tracker-connect"
    },
    buttonSecondary: {
      text: "Abbrechen"
    }
  });
};

/**
 * Zeigt den Dialog zur Trainingserfassung an
 */
const showTrainingRecordDialog = () => {
  showDialog({
    title: "Wann hast Du trainiert?",
    text: unsafeHTML("Toll, dass Du Deine Tageschallenge erfolgreich durchgeführt hast.<br>Nach Deiner Bestätigung wird überprüft, ob Dein Fitness-Tracker etwas aufgezeichnet hat und Du ausreichend trainiert hast. Das kann bis zu zwei Tage dauern."),
    icon: iconTagSport,
    iconColor: "var(--bubble-yellow)",
    iconCorner: "top-left",
    buttonPrimary: {
      text: "Ich habe heute trainiert",
      onClick: () => {
        // Aktuelle Challenge-Daten abrufen
        const currentChallenge = appStorage._data.challenges?.ploggingActive;
        if (!currentChallenge) {
          console.error("Challenge-Daten nicht gefunden");
          return;
        }

        const currentCompletedTrainings = currentChallenge.completedTrainings || 0;
        const totalTrainings = currentChallenge.totalTrainings || 4;

        // Prüfen, ob bereits alle Trainings absolviert wurden
        if (currentCompletedTrainings >= totalTrainings) {
          console.log("Alle Trainings bereits absolviert");
          return;
        }

        // Training erfassen
        const newCompletedTrainings = currentCompletedTrainings + 1;
        appStorage.updateChallenge('ploggingActive', {
          completedTrainings: newCompletedTrainings
        });

        console.log(`Training erfasst: ${newCompletedTrainings} von ${totalTrainings}`);
      }
    },
    buttonSecondary: {
      text: "Abbrechen"
    }
  });
};

/**
 * Template für die "Plogging-Challenge" Challenge-Seite
 * @returns {TemplateResult} Das Challenge-Seiten Template
 */
export const templatePloggingActive = () => {
  // Healthgoal-Level für Debugging ausgeben
  console.log('Challenge Plogging-Active - Healthgoal-Level:', appStorage._data.healthGoalLevel);
  console.log('Challenge Plogging-Active - Healthgoal aktiv:', appStorage._data.activeHealthGoals.aktivInDerNatur);

  // Nach dem Rendern das Drag-Scrolling initialisieren
  setTimeout(() => {
    refreshDragScroll('.image-card-narrow-container');
  }, 100);

  // Prüfen, ob die Challenge bereits gestartet wurde
  const appData = getAppData();
  const challengeStarted = appData.challenges &&
                          appData.challenges.ploggingActive &&
                          appData.challenges.ploggingActive.started;

  // Daten für die Challenge
  const completedTrainings = challengeStarted ? appStorage._data.challenges.ploggingActive.completedTrainings : 0;
  const totalTrainings = challengeStarted ? appStorage._data.challenges.ploggingActive.totalTrainings : 4;

  // Prüfen, ob alle Trainings abgeschlossen sind
  const allTrainingsCompleted = challengeStarted && completedTrainings >= totalTrainings;

  // Debug-Ausgaben
  console.log('Challenge Status:', {
    challengeStarted,
    completedTrainings,
    totalTrainings,
    allTrainingsCompleted,
    challengeData: appStorage._data.challenges?.ploggingActive
  });

  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <h2 class="content-no-bottom-margin content-padding">
        <span class="dark-grn-text">Plogging-Challenge</span>
      </h2>

      <!-- Pills Container -->
      <div class="content-padding">
        ${pillsContainer([
          { text: "30 Min.", color: "--pill-green-background", textColor: "--pill-green-text" },
          { text: "14 Tage", color: "--pill-green-background", textColor: "--pill-green-text", iconName: iconTimelater, iconPosition: "left" },
          { text: "auch ohne Fitness-Armband möglich", color: "--pill-green-background", textColor: "--pill-green-text", iconName: iconInfo, iconPosition: "right" }
        ])}
      </div>

      <!-- Segmented Control (Tab Bar) -->
      <div id="segmentedControlContainer" class="standard-container">
        ${segmentedControl.template}
      </div>

      <!-- Erfahre mehr Text -->
      <p class="content-padding black-text">Erfahre hier mehr.</p>

      <!-- Info Button -->
      <div class="standard-container content-padding">
        ${buttonTextIcon("Informationen und Quellen", iconInfo, "left")}
      </div>

      <!-- Start/Erfassen Button - nur anzeigen wenn Healthgoal aktiv, Niveau bestimmt und keine andere Challenge aktiv -->
      ${(() => {
        const hasActiveChallenge = appStorage.hasActiveChallenge();
        const isThisChallengeActive = appStorage.isChallengeActive('ploggingActive');
        const healthGoalLevel = appStorage.getHealthGoalLevelForGoal('aktivInDerNatur') || appStorage._data.healthGoalLevel;
        const canShowButton = appStorage._data.activeHealthGoals.aktivInDerNatur &&
                             healthGoalLevel &&
                             (!hasActiveChallenge || isThisChallengeActive);
        return canShowButton;
      })() ? html`
        <div class="standard-container content-padding ${challengeStarted ? (allTrainingsCompleted ? 'training-done-container-inactive' : 'training-done-container') : ''}">
          ${challengeStarted ? html`
            <!-- Paragraph für Training erfassen -->
            <p class="content-top-padding caption center-text">
              ${allTrainingsCompleted
                ? "Du hast Dein Training für heute bereits erfasst. Morgen kannst Du die nächste Tageschallenge absolvieren und hier erfassen."
                : "Bestätige mir hier, dass Du trainiert hast, damit Deine Aktivität überprüft werden kann."
              }
            </p>
          ` : ''}

          ${!allTrainingsCompleted ? buttonStandard({
            text: challengeStarted ? "Training erfassen" : "Challenge jetzt starten",
            variant: "primary",
            onClick: challengeStarted
              ? showTrainingRecordDialog
              : showConnectionDialog
          }) : ''}
        </div>
      ` : html`
        <!-- Button ausgeblendet: Bedingungen nicht erfüllt -->
        <div class="standard-container content-padding">
          <p class="caption black-text">
            ${!appStorage._data.activeHealthGoals.aktivInDerNatur
              ? "Aktiviere zuerst das Gesundheitsziel, um Challenges zu starten."
              : !healthGoalLevel
                ? "Bestimme zuerst Dein Healthgoal-Niveau, um Challenges zu starten."
                : appStorage.hasActiveChallenge() && !appStorage.isChallengeActive('ploggingActive')
                  ? "Du hast bereits eine andere Challenge gestartet. Schließe diese zuerst ab oder setze sie zurück."
                  : "Challenge kann nicht gestartet werden."
            }
          </p>
        </div>
      `}
    </div>
    <style>
      .listItemText {
        cursor: pointer;
      }
    </style>
  `;
};

/**
 * Initialisiert die Reset-Menu-Funktionalität für die Challenge
 * Sollte nach dem Rendern der Seite aufgerufen werden
 */
export const initializePloggingActiveResetMenu = () => {
  setTimeout(() => {
    setupChallengeResetMenu(
      "challenge-plogging-active-menu",
      "ploggingActive",
      "Plogging-Challenge",
      () => {
        // Callback nach Reset - zurück zur Health Goal Seite
        console.log("Challenge Plogging-Active wurde zurückgesetzt, navigiere zurück...");
        const backTarget = appStorage._data.activeHealthGoals.aktivInDerNatur
          ? "/healthgoals-overview/hg-aktivInDerNatur-active"
          : "/healthgoals-overview/hg-aktivInDerNatur";
        router.navigate(backTarget);
      }
    );
  }, 100); // Kurze Verzögerung um sicherzustellen, dass das DOM gerendert ist
};

console.log("Challenge Plogging-Active loaded");
