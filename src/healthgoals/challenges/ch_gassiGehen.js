import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconInfo from "../../../svg/icons/icon_info.svg";
import iconTimelater from "../../../svg/icons/icon_timelater.svg";
import { router, exitWithSlide } from "../../../router.js";
import { buttonTextIcon } from "../../../webcomponents/buttonTextIcon.js";
import { setupChallengeResetMenu } from "../../../helpers/reset-helper.js";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { infoCardBullets } from "../../../webcomponents/infoCardBullets.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { createSegmentedControl } from "../../../webcomponents/segmentedControl.js";
import { pillsContainer } from "../../../webcomponents/pills.js";
import iconChevronRight from "../../../svg/icons/icon_chevron_right.svg";
import { listItem } from "../../../webcomponents/listItem.js";
import { appStorage } from "../../../utils.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { imageCardNarrow } from "../../../webcomponents/imageCardNarrow.js";
import imgGassiGehen from "../../../img/healthgoals/hg_gassiGehen.jpg";
import { refreshDragScroll } from "../../../helpers/dragScroll.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Bestimme das Zurück-Ziel basierend auf dem aktiven Status des Gesundheitsziels
  const backTarget = appStorage._data.activeHealthGoals.fitUmgebung
    ? "/healthgoals-overview/hg-fitUmgebung-active"
    : "/healthgoals-overview/hg-fitUmgebung";

  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget),
      menuId: "challenge-gassi-gehen-menu"
    })}
  `;
};

/**
 * List Item Content
 */
const trainingsplan = [
  {
    text: "1. Training: 2 - 4 km Gassigehen",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/gassi-gehen")
  },
  {
    text: "2. Training: 2 - 4 km Gassigehen",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/gassi-gehen")
  },
  {
    text: "3. Training: 2 - 4 km Gassigehen",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/gassi-gehen")
  },
  {
    text: "4. Training: 2 - 4 km Gassigehen",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/gassi-gehen")
  }
];

/**
 * Segmented Control für die Tabs "Übung" und "Trainingsplan"
 */
const segments = [
  {
    id: "uebung",
    title: "Übung",
    content: html`
      <div class="tabpadding black-text">
        <!-- Beschreibungstext -->
        <p class="challenge-description content-padding">
Du möchtest Dich im Freien bewegen, dabei etwas Sinnvolles tun und würdest Dich über Gesellschaft freuen? Wie wäre es mit einem Ehrenamt bei einem Tierheim in Deiner Nähe? Dort warten viele dankbare Fellnasen, die sich über lange Spaziergänge sehr freuen werden.        </p>

        <!-- Grüne Info-Card mit Bullet Points -->
        <div class="standard-container content-padding">
          ${infoCardBullets({
            title: "Was wirst Du erreichen?",
            bulletPoints: [
              "Durch die Aktivität an der frischen Luft wird Dein Herz-Kreislauf-System gestärkt.",
              "Deine Fettverbrennung wird angekurbelt.",
              "Spaziergänge mit Hund können stressreduzierend wirken und die Stimmung heben."
            ],
            background: "--info-card-background-green"
          })}
        </div>

          ${sectionTitle("Diese Übungen erwarten Dich")}
        <!-- Image Cards -->
         <div class="image-card-narrow-scroll-container">
          <div class="standard-container content-padding image-card-narrow-container">
          ${imageCardNarrow({
            imgPath: imgGassiGehen,
            title: "Gassigehen",
            altText: "Gassigehen",
            link: "/training/gassi-gehen"
          })}
          ${imageCardNarrow({
            imgPath: imgGassiGehen,
            title: "Gassigehen",
            altText: "Gassigehen",
            link: "/training/gassi-gehen"
          })}
          ${imageCardNarrow({
            imgPath: imgGassiGehen,
            title: "Gassigehen",
            altText: "Gassigehen",
            link: "/training/gassi-gehen"
          })}
          ${imageCardNarrow({
            imgPath: imgGassiGehen,
            title: "Gassigehen",
            altText: "Gassigehen",
            link: "/training/gassi-gehen"
          })}
          </div>
        </div>

        <!-- Gelbe Info-Card mit Vorsichtsmaßnahmen -->
        <div class="standard-container content-padding">
          ${infoCardPlain({
            title: "Vorsichtsmaßnahmen",
            text: unsafeHTML(`Du solltest die Challenge nicht weiter ausführen, wenn während der Übung akute Schmerzen in den Gelenken oder Muskeln auftreten.
            <p>Darüber hinaus darf die Challenge in folgenden Fällen nicht absolviert werden:</p>
              <ul>
              <li>Bei akuten Erkrankungen</li>
              <li>Nach operativen Eingriffen</li>
            </ul>
            <p>Bei chronischen Erkrankungen halte bitte Rücksprache mit Deinem Arzt.</p>
            <p>Schwangere, sowie Menschen die kürzlich entbunden haben, sollten diese Challenge nicht durchführen. Bitte halte Rücksprache mit Deinem Arzt.`),
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
      </div>
    `
  },
  {
    id: "trainingsplan",
    title: "Trainingsplan",
    content: html`
      <div class="tabpadding">
        <div class="content-padding content-no-top-padding">
          ${listItem(trainingsplan)}
        </div>
      </div>
    `
  }
];

const segmentedControl = createSegmentedControl(segments);

/**
 * Template für die "Gassigehen mit Hund" Challenge-Seite
 * @returns {TemplateResult} Das Challenge-Seiten Template
 */
export const templateGassiGehen = () => {
  // Healthgoal-Level für Debugging ausgeben
  console.log('Challenge Gassigehen - Healthgoal-Level:', appStorage._data.healthGoalLevel);
  console.log('Challenge Gassigehen - Healthgoal aktiv:', appStorage._data.activeHealthGoals.fitUmgebung);

  // Nach dem Rendern das Drag-Scrolling initialisieren
  setTimeout(() => {
    refreshDragScroll('.image-card-narrow-container');
  }, 100);

  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <h2 class="content-no-bottom-margin content-padding">
        <span class="dark-grn-text">Gassigehen mit Hund</span>
      </h2>

      <!-- Pills Container -->
      <div class="content-padding">
        ${pillsContainer([
          { text: "2 bis 4 km", color: "--pill-green-background", textColor: "--pill-green-text" },
          { text: "14 Tage", color: "--pill-green-background", textColor: "--pill-green-text", iconName: iconTimelater, iconPosition: "left" },
          { text: "auch ohne Fitness-Armband möglich", color: "--pill-green-background", textColor: "--pill-green-text", iconName: iconInfo, iconPosition: "right" }
        ])}
      </div>

      <!-- Segmented Control (Tab Bar) -->
      <div id="segmentedControlContainer" class="standard-container">
        ${segmentedControl.template}
      </div>

      <!-- Erfahre mehr Text -->
      <p class="content-padding black-text">Erfahre hier mehr.</p>

      <!-- Info Button -->
      <div class="standard-container content-padding">
        ${buttonTextIcon("Informationen und Quellen", iconInfo, "left")}
      </div>

      <!-- Start Button - nur anzeigen wenn Healthgoal aktiv, Niveau bestimmt und keine andere Challenge aktiv -->
      ${(() => {
        const hasActiveChallenge = appStorage.hasActiveChallenge();
        const isThisChallengeActive = appStorage.isChallengeActive('gassiGehen');
        const canShowButton = appStorage._data.activeHealthGoals.fitUmgebung &&
                             appStorage._data.healthGoalLevel &&
                             (!hasActiveChallenge || isThisChallengeActive);
        return canShowButton;
      })() ? html`
        <div class="standard-container content-padding">
          ${buttonStandard({
            text: "Challenge jetzt starten",
            variant: "primary"
          })}
        </div>
      ` : html`
        <!-- Button ausgeblendet: Bedingungen nicht erfüllt -->
        <div class="standard-container content-padding">
          <p class="caption black-text">
            ${!appStorage._data.activeHealthGoals.fitUmgebung
              ? "Aktiviere zuerst das Gesundheitsziel, um Challenges zu starten."
              : !appStorage._data.healthGoalLevel
                ? "Bestimme zuerst Dein Healthgoal-Niveau, um Challenges zu starten."
                : appStorage.hasActiveChallenge() && !appStorage.isChallengeActive('gassiGehen')
                  ? "Du hast bereits eine andere Challenge gestartet. Schließe diese zuerst ab oder setze sie zurück."
                  : "Challenge kann nicht gestartet werden."
            }
          </p>
        </div>
      `}
    </div>
  `;
};

/**
 * Initialisiert die Reset-Menu-Funktionalität für die Challenge
 * Sollte nach dem Rendern der Seite aufgerufen werden
 */
export const initializeGassiGehenResetMenu = () => {
  setTimeout(() => {
    setupChallengeResetMenu(
      "challenge-gassi-gehen-menu",
      "gassiGehen",
      "Gassigehen mit Hund",
      () => {
        // Callback nach Reset - zurück zur Health Goal Seite
        console.log("Challenge Gassigehen mit Hund wurde zurückgesetzt, navigiere zurück...");
        const backTarget = appStorage._data.activeHealthGoals.fitUmgebung
          ? "/healthgoals-overview/hg-fitUmgebung-active"
          : "/healthgoals-overview/hg-fitUmgebung";
        router.navigate(backTarget);
      }
    );
  }, 100); // Kurze Verzögerung um sicherzustellen, dass das DOM gerendert ist
};

console.log("Challenge Gassigehen mit Hund loaded");
