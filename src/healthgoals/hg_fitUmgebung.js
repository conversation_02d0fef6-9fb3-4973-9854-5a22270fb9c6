import { html } from "lit-html";
import { BottomNavigation } from "../bottomNavigation.js";
import { topMenu } from "../../webcomponents/topMenu.js";
import { imageCard } from "../../webcomponents/imageCard.js";
import imgKomootHG from "../../img/healthgoals/komoot_hg.jpg";
import iconArrowLeft from "../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../svg/icons/PointsVertical.svg";
import iconMoneypig from "../../svg/icons/icon_moneypig.svg";
import iconTime from "../../svg/icons/icon_timelater.svg";
import iconInfo from "../../svg/icons/icon_info.svg";
import iconCalendar from "../../svg/icons/icon_calendar.svg";
import { captionUppercase } from "../../webcomponents/captionUppercase.js";
import { Tag } from "../../webcomponents/tag.js";
import { sectionParagraph } from "../../webcomponents/sectionParagraph.js";
import { infoCardBullets } from "../../webcomponents/infoCardBullets.js";
import { infoCardwButton } from "../../webcomponents/infoCardwButton.js";
import imgKomoot from "../../img/komootLogo.png";
import imgLockereWanderung from "../../img/healthgoals/hg_lockereWanderung.jpg";
import imgGassiGehen from "../../img/healthgoals/hg_gassiGehen.jpg";
import imgFahrradTour from "../../img/healthgoals/hg_fahrradTour.jpg";
import imgSpazierenGehen from "../../img/healthgoals/hg_spazierenGehen.jpg";
import imgPlogging from "../../img/healthgoals/hg_plogging.jpg";
import { buttonTextIcon } from "../../webcomponents/buttonTextIcon.js";
import { buttonStandard } from "../../webcomponents/buttonStandard.js";
import { titleDuoColor } from "../../webcomponents/titleDuoColor.js";
import { exitWithSlide, router } from "../../router.js"; // Router importieren
import { challengeCard } from "../../webcomponents/challengeCard.js";
import { appStorage, isChallengeCompleted } from "../../utils.js"; // Stelle sicher, dass appStorage importiert wird
import { setupHealthGoalResetMenu } from "../../helpers/reset-helper.js";

/* Top Menu */
const templateTopMenu = (isActive = false) => html`
  ${topMenu({
    backIcon: iconArrowLeft,
    primaryIcon: "",
    menuIcon: iconDotMenu,
    onBack: () => exitWithSlide("/healthgoals-overview"), // Ändern von "/healthgoals-overview-no-goal" zu "/healthgoals-overview"
    onClose: null, // Kein onClose - Reset-Menu wird über Event-Handler gesteuert
    menuId: isActive ? "health-goal-active-menu" : "health-goal-menu"
  })}
`;

// Hilfsfunktion für Challenge-Status
const getChallengeStatusPill = (challengeKey) => {
  if (isChallengeCompleted(challengeKey)) {
    return {
      text: "Abgeschlossen",
      type: "done"
    };
  } else if (appStorage.isChallengeActive(challengeKey)) {
    return {
      text: "Gerade aktiv",
      type: "active"
    };
  }
  return null;
};

// Haupttemplate mit Parameter für den Aktivitätsstatus
export const hg_fitUmgebungTemplate = (isActive = false) => {
  console.log("hg_fitUmgebungTemplate aufgerufen mit isActive:", isActive);
  console.log("appStorage._data.activeHealthGoals:", appStorage._data.activeHealthGoals);
  console.log("appStorage._data.healthGoalLevel:", appStorage._data.healthGoalLevel);

  // Hilfsfunktion für Niveau-Text
  const getLevelDisplayText = (level) => {
    switch(level) {
      case 'beginner': return 'Einsteiger';
      case 'intermediate': return 'Fortgeschritten';
      case 'advanced': return 'Profi';
      default: return null;
    }
  };

  const levelText = getLevelDisplayText(appStorage._data.healthGoalLevel);
  const hasLevel = appStorage._data.healthGoalLevel !== null;

  return html`
    ${templateTopMenu(isActive)}
    <div class="content-left-align content-padding content-top-padding">
      ${captionUppercase("Gesundheitsziel")}
      <h2 class="content-no-bottom-margin">
        <span class="dark-grn-text">Fit in Deiner Umgebung</span>
        <span class="contrast-grn-text"></span>
      </h2>
      <p class="caption black-text">in Kooperation mit komoot</p>
    </div>
    <!-- Main Content -->
    <!-- Image Card - aktives Ziel -->
    <div class="standard-container content-padding">
      ${imageCard({
        cardImage: imgKomootHG,
        pillText: isActive ? "Gerade aktiv" : "",
        pillColor: isActive ? "--accent-blue" : "",
      })}
      <div class="tags-big">
        ${Tag("--tag-green", iconMoneypig, "1500 Punkte", "--tag-text-green")}
        ${Tag("--tag-green", iconTime, "max. 90 Tage", "--tag-text-green")}
        ${levelText ? Tag("--tag-green", null, levelText, "--tag-text-green") : ''}
      </div>
    </div>
    ${sectionParagraph(
      "Werde aktiv und erkunde Deine Umgebung! Ob zu Fuß oder per Rad – Mit unserem Partner komoot entdeckst Du spannende Touren in Deiner Umgebung."
    )}
    <!-- Ziel Kachel -->
    <div class="content-padding standard-container content-bottom-padding">
      ${infoCardBullets({
        icoPath: "",
        title: "Dieses Ziel ist etwas für Dich, um beispielsweise ...",
        bulletPoints: [
          "... Dein Herz-Kreislauf-System zu stärken",
          "... mehr Zeit in der Natur zu verbringen.",
          "... Du durch Bewegung Deine Umgebung kennenlernen möchtest.",
          "... Dein Immunsystem zu unterstützen.",
        ],
      })}
    </div>
    <!-- Komoot Kachel -->
    <div class="content-padding standard-container content-bottom-padding">
      <span class="rounded-img">
        ${infoCardwButton({
          icoPath: imgKomoot,
          title: "Mein Partner komoot",
          text: "Lass' Dich von meinem Partner komoot mit Routen inspirieren und durch die Natur leiten, um dieses Ziel zu erreichen.",
          caption: "",
          linkText: "Mehr erfahren",
          background: "",
        })}
      </span>
    </div>
    <!-- Paragraph -->
    ${sectionParagraph("Das erwartet Dich:", true)}
    ${sectionParagraph(
      "Du kannst später aus folgenden Challenges auswählen. Die Reihenfolge bestimmst Du dabei selbst. Wiederhole auch gern Challenges, die Dir gefallen haben. Sobald Du mindestens vier Challenges erfolgreich absolviert hast, hast Du Dein Ziel erreicht. Du hast dafür bis zu 90 Tage Zeit. Du kannst es aber auch schneller schaffen."
    )}
    <!-- Challenge Card -->
    <div class="content-padding standard-container content-top-padding content-bottom-padding gap-items-16">
      <a data-navigate="/challenge/lockere-wanderung">
        ${challengeCard({
          imgPath: imgLockereWanderung,
          title: "Lockere Wanderung",
          infoItems: [
            { icoPath: iconTime, text: "90 Min. pro Training" },
            { icoPath: iconCalendar, text: "14 Tage, 1 Mal pro Woche" },
          ],
          statusPill: getChallengeStatusPill("lockereWanderung")
        })}
      </a>
      <a data-navigate="/challenge/gassi-gehen">
        ${challengeCard({
          imgPath: imgGassiGehen,
          title: "Gassigehen mit Hund",
          infoItems: [
            { icoPath: iconTime, text: "30 Min. pro Training" },
            { icoPath: iconCalendar, text: "14 Tage, 2 Mal pro Woche" },
          ],
          statusPill: getChallengeStatusPill("gassiGehen")
        })}
      </a>
      <a data-navigate="/challenge/fahrrad-tour">
        ${challengeCard({
          imgPath: imgFahrradTour,
          title: "Fahrrad-Tour",
          infoItems: [
            { icoPath: iconTime, text: "30 Min. pro Training" },
            { icoPath: iconCalendar, text: "14 Tage, 2 Mal pro Woche" },
          ],
          statusPill: getChallengeStatusPill("fahrradTour")
        })}
      </a>
      <a data-navigate="/challenge/spazieren-gehen">
        ${challengeCard({
          imgPath: imgSpazierenGehen,
          title: "Spazieren gehen",
          infoItems: [
            { icoPath: iconTime, text: "30 Min. pro Training" },
            { icoPath: iconCalendar, text: "14 Tage, 2 Mal pro Woche" },
          ],
          statusPill: getChallengeStatusPill("spazierenGehen")
        })}
      </a>
      <a data-navigate="/challenge/plogging">
        ${challengeCard({
          imgPath: imgPlogging,
          title: "Joggen und Müll sammeln (Plogging)",
          infoItems: [
            { icoPath: iconTime, text: "1-2 km pro Training" },
            { icoPath: iconCalendar, text: "14 Tage, 2 Mal pro Woche" },
          ],
          statusPill: getChallengeStatusPill("plogging")
        })}
      </a>
    </div>
    <!-- Zitat -->
    <div
      class="standard-container content-padding content-no-bottom-margin center-text"
    >
      ${titleDuoColor("&bdquo;Entdecke", " Deine Umgebung neu.&ldquo;", "h2")}
    </div>
    ${sectionParagraph("Erfahre hier mehr.")}
    <div class="standard-container content-padding content-bottom-padding">
      ${buttonTextIcon("Informationen und Quellen", iconInfo, "left")}
    </div>
    <!-- Weiter Button - nur anzeigen wenn Niveau noch nicht bestimmt wurde und kein anderes Gesundheitsziel aktiv ist -->
    ${!hasLevel ? html`
      ${(() => {
        // Prüfen, ob ein anderes Gesundheitsziel bereits aktiv ist
        const hasOtherActiveHealthGoal = Object.entries(appStorage._data.activeHealthGoals || {})
          .some(([key, value]) => key !== 'fitUmgebung' && value === true);

        if (hasOtherActiveHealthGoal) {
          return html`
            <div class="standard-container content-padding">
              <p class="caption black-text">
                Du kannst dieses Gesundheitsziel noch nicht neu starten, da Du aktuell ein anderes Gesundheitsziel aktiv hast. Bitte schließe dieses erst ab oder setze es zurück.
              </p>
            </div>
          `;
        } else {
          return html`
            <div class="standard-container content-padding">
              ${buttonStandard({
                text: "Zum nächsten Schritt",
                variant: "primary",
                link: "/consent",
              })}
            </div>
          `;
        }
      })()}
    ` : ''}
    ${BottomNavigation("home")}
  `;
};

/**
 * Initialisiert die Reset-Menu-Funktionalität für die Health Goal Seite
 * Sollte nach dem Rendern der Seite aufgerufen werden
 * @param {boolean} isActive - Ob das Gesundheitsziel aktiv ist
 */
export const initializeHealthGoalResetMenu = (isActive = false) => {
  // Reset-Menu für beide Zustände einrichten (aktiv und inaktiv)
  setTimeout(() => {
    const menuId = isActive ? "health-goal-active-menu" : "health-goal-menu";
    setupHealthGoalResetMenu(
      menuId,
      "fitUmgebung",
      "Fit in deiner Umgebung"
    );
  }, 100); // Kurze Verzögerung um sicherzustellen, dass das DOM gerendert ist
};

console.log("HG Fit in deiner Umgebung loaded");
