import { html } from "lit-html";
/* <PERSON><PERSON> wird hier <PERSON><PERSON><PERSON><PERSON>, da wir nicht die komplette Seite neu laden über den Router sondern nur die Inhalte aktualisieren wollen, nach jedem step */
import { render } from "lit-html";
import { progressBar } from "../../webcomponents/progressBar.js";
import { buttonStandard } from "../../webcomponents/buttonStandard.js";
import { radioPoll } from "../../webcomponents/radioPoll.js";
import { topMenu } from "../../webcomponents/topMenu.js";
import iconArrowLeft from "../../svg/icons/ArrowLeft.svg";
import iconArrowRight from "../../svg/icons/ArrowRight.svg";
import iconClose from "../../svg/icons/icon_close.svg";
import iconLift from "../../svg/icons/icon_lift.svg";
import { buttonTextIcon } from "../../webcomponents/buttonTextIcon.js";
import imgLegs from "../../img/healthgoals/poll_legs.png";
import { infoCardPlain } from "../../webcomponents/infoCardPlain.js";
import { titleDuoColor } from "../../webcomponents/titleDuoColor.js";
import { sectionParagraph } from "../../webcomponents/sectionParagraph.js";
import { accordion } from "../../webcomponents/accordion.js";
import { appStorage } from "../../utils.js";
import { router } from "../../router.js"; // Router importieren

/*
  ====================
  STATE MANAGEMENT
  ====================
*/
let currentStep = 1; // Modulweiter State für aktuellen Schritt
let isAccordionOpen = false; // State für das Accordion
let pollAnswers = {}; // Speichert die Antworten des Polls (step -> answerIndex)
let calculatedLevel = null; // Das berechnete Niveau

/*
  ====================
  KONSTANTEN & KONFIGURATION
  ====================
*/
const steps = [
  {
    id: 1,
    title: "An wie vielen Tagen bist Du aktuell aktiv?",
    options: [
      "1 bis 2 Tage pro Woche",
      "3 bis 5 Tage pro Woche",
      "6 bis 7 Tage pro Woche",
    ],
  },
  {
    id: 2,
    title: "3-Minuten-Stufentest",
    description:
      "Führe jetzt diese Übung 3 Minuten aus. Miss bitte direkt im Anschluss Deinen Puls.",
  },
  {
    id: 3,
    title: "Wie anstrengend war der Test?",
    options: [
      "Wenig anstrengend",
      "Mittelmäßig anstrengend",
      "Sehr anstrengend",
    ],
  },
];

/*
  ====================
  HELFERFUNKTIONEN
  ====================
*/
const renderProgressBar = (step) => {
  const completedChallenges = step;
  const totalChallenges = steps.length;
  return progressBar({
    completedChallenges,
    totalChallenges,
    type: "poll",
  });
};

/**
 * Berechnet das Healthgoal-Niveau basierend auf den Poll-Antworten
 * Erste Antwort = beginner, Zweite = intermediate, Dritte = advanced
 * Bei Gleichstand wird das niedrigere Niveau gewählt
 */
const calculateHealthGoalLevel = () => {
  const levelCounts = { beginner: 0, intermediate: 0, advanced: 0 };

  console.log('Poll answers:', pollAnswers);

  // Nur Steps mit Optionen berücksichtigen
  steps.forEach(step => {
    if (!step.options) return; // Step hat keine Antwortoptionen

    const answerIndex = pollAnswers[step.id];
    console.log(`Step ${step.id}: Answer index ${answerIndex}`);

    if (answerIndex !== undefined && answerIndex !== null) {
      const parsedIndex = parseInt(answerIndex, 10);
      if (parsedIndex === 0) levelCounts.beginner++;
      else if (parsedIndex === 1) levelCounts.intermediate++;
      else if (parsedIndex === 2) levelCounts.advanced++;
    }
  });

  console.log('Level counts:', levelCounts);

  // Bestimme das Niveau (bei Gleichstand das niedrigere wählen)
  if (levelCounts.beginner >= levelCounts.intermediate && levelCounts.beginner >= levelCounts.advanced) {
    return 'beginner';
  } else if (levelCounts.intermediate > levelCounts.beginner && levelCounts.intermediate >= levelCounts.advanced) {
    return 'intermediate';
  } else {
    return 'advanced';
  }
};

/**
 * Gibt den deutschen Text für ein Niveau zurück
 */
const getLevelText = (level) => {
  switch(level) {
    case 'beginner': return 'Einsteiger-Niveau';
    case 'intermediate': return 'Fortgeschrittenen-Niveau';
    case 'advanced': return 'Profi-Niveau';
    default: return 'Einsteiger-Niveau';
  }
};

/**
 * Gibt das alternative Niveau für das Accordion zurück
 */
const getAlternativeLevel = (level) => {
  switch(level) {
    case 'beginner': return 'intermediate';
    case 'intermediate': return 'beginner';
    case 'advanced': return 'intermediate';
    default: return 'intermediate';
  }
};

/* Top Menu */
const handleBack = () => {
  if (currentStep > 1) {
    currentStep = currentStep > steps.length ? steps.length : currentStep - 1;
    updateUI();
  } else {
    window.history.back(); // Navigation je nachdem ob Schritt 2 schon erreicht wurde
  }
};

const handleClose = () => {
  window.history.back(); // Direktes Zurücknavigieren ohne Schrittwechsel
};

const templateTopMenu = () => html`
  ${topMenu({
    backIcon: iconArrowLeft,
    primaryIcon: "",
    menuIcon: iconClose,
    onBack: handleBack,
    onClose: handleClose, // Schließen-Funktion
  })}
`;

/*
  ====================
  EVENT-HANDLING
  ====================
*/
// Toggle-Funktion für das Accordion
const toggleAccordion = () => {
  isAccordionOpen = !isAccordionOpen;
  updateUI();
};

const handleNextStep = () => {
  if (currentStep <= steps.length) {
    // Sammle die Antwort für den aktuellen Schritt (nur für Steps mit Antworten)
    const step = steps.find(s => s.id === currentStep);
    if (step && step.options) {
      const selectedRadio = document.querySelector(`input[name="step-${currentStep}"]:checked`);
      if (selectedRadio) {
        const answerIndex = parseInt(selectedRadio.value, 10);
        pollAnswers[currentStep] = answerIndex;
        console.log(`Antwort für Step ${currentStep}:`, answerIndex, step.options[answerIndex]);
      }
    }

    // Wenn wir alle Schritte durchlaufen haben, berechne das Niveau
    if (currentStep === steps.length) {
      calculatedLevel = calculateHealthGoalLevel();
      console.log('Berechnetes Niveau:', calculatedLevel);
    }

    // Erlaube currentStep > steps.length für finale Auswertung
    currentStep += 1;
    updateUI();
  }
};

const handleRadioChange = () => {
  const buttonElement = document.getElementById(`button-step-${currentStep}`);
  if (buttonElement) {
    buttonElement.removeAttribute("disabled");
    buttonElement.classList.remove("disabled");
  }
};

const setupEventListeners = () => {
  if (currentStep > steps.length) {
    // Event-Listener für Abschluss-Button (Primary)
    const completeButton = document.getElementById("button-complete");
    if (completeButton) {
      completeButton.addEventListener("click", () => {
        // Hole den aktuellen Gesundheitsziel-Kontext
        const currentHealthGoal = appStorage.getCurrentHealthGoalContext() || 'fitUmgebung';

        // Setze das berechnete Niveau für das spezifische Gesundheitsziel
        if (calculatedLevel) {
          appStorage.setHealthGoalLevelForGoal(currentHealthGoal, calculatedLevel);
          // Auch legacy-Feld setzen für Rückwärtskompatibilität
          appStorage.setHealthGoalLevel(calculatedLevel);
        }

        // Aktiviere das entsprechende Ziel im App-State
        appStorage.activateHealthGoal(currentHealthGoal);

        // Navigiere zur entsprechenden Gesundheitsziel-Seite
        const targetUrl = `/healthgoals-overview/hg-${currentHealthGoal === 'aktivInDerNatur' ? 'aktivInDerNatur' : 'fitUmgebung'}-active`;
        console.log(`Button geklickt, navigiere zu ${targetUrl}`);

        // Verwende den Router statt window.location.href
        router.navigate(targetUrl);
      });
    } else {
      console.error("Button 'button-complete' nicht gefunden!");
    }

    // Event-Listener für Accordion-Button (Alternative)
    const accordionButton = document.querySelector('.accordion-button');
    if (accordionButton) {
      accordionButton.addEventListener("click", (e) => {
        e.preventDefault(); // Verhindere die Standard-Link-Navigation

        // Hole den aktuellen Gesundheitsziel-Kontext
        const currentHealthGoal = appStorage.getCurrentHealthGoalContext() || 'fitUmgebung';

        // Setze das alternative Niveau für das spezifische Gesundheitsziel
        const alternativeLevel = getAlternativeLevel(calculatedLevel || 'beginner');
        appStorage.setHealthGoalLevelForGoal(currentHealthGoal, alternativeLevel);
        // Auch legacy-Feld setzen für Rückwärtskompatibilität
        appStorage.setHealthGoalLevel(alternativeLevel);

        // Aktiviere das entsprechende Ziel im App-State
        appStorage.activateHealthGoal(currentHealthGoal);

        // Navigiere zur entsprechenden Gesundheitsziel-Seite
        const targetUrl = `/healthgoals-overview/hg-${currentHealthGoal === 'aktivInDerNatur' ? 'aktivInDerNatur' : 'fitUmgebung'}-active`;
        console.log(`Accordion-Button geklickt, navigiere zu ${targetUrl}`);

        // Verwende den Router statt window.location.href
        router.navigate(targetUrl);
      });
    }
    return;
  }
  const radioButtons = document.querySelectorAll(
    `input[name="step-${currentStep}"]`
  );
  const buttonElement = document.getElementById(`button-step-${currentStep}`);

  // Radio-Buttons
  if (radioButtons) {
    radioButtons.forEach((radio) => {
      radio.addEventListener("change", handleRadioChange);
    });
  }

  // Weiter-Button
  if (buttonElement) {
    buttonElement.addEventListener("click", handleNextStep);
  }
};

const cleanupEventListeners = () => {
  const radioButtons = document.querySelectorAll(
    `input[name="step-${currentStep}"]`
  );
  const buttonElement = document.getElementById(`button-step-${currentStep}`);

  // Alte Event-Listener entfernen
  if (radioButtons) {
    radioButtons.forEach((radio) => {
      radio.removeEventListener("change", handleRadioChange);
    });
  }

  if (buttonElement) {
    buttonElement.removeEventListener("click", handleNextStep);
  }
};

/*
  ====================
  RENDER-LOGIK
  ====================
*/

// Aktualisierte renderStepContent
const renderStepContent = () => {
  if (currentStep > steps.length) {
    // Abschlussseite
    const level = calculatedLevel || 'beginner';
    const levelText = getLevelText(level);
    const alternativeLevel = getAlternativeLevel(level);
    const alternativeLevelText = getLevelText(alternativeLevel);

    return html`
      <!-- Top Menu -->
      ${templateTopMenu()}
      <!-- Content -->
      <div class="niveau-poll-container content-top-padding">
        <div class="content-padding standard-container content-left-align">
          ${progressBar({
            completedChallenges: steps.length,
            totalChallenges: steps.length,
            type: "poll",
          })}
        </div>

        <!-- Überschrift -->
        <div class="content-padding">
          ${titleDuoColor("Dein individuelles", "Leistungsniveau")}
        </div>

        <!-- Info Card -->
        <div class="content-padding">
          ${infoCardPlain({
            icon: iconLift,
            caption: `${levelText} – Ich rate Dir damit zu starten.`,
            textColor: "var(--caption-green)",
            backgroundColor: "var(--info-card-background-green)",
          })}
        </div>

        <!-- Paragraph -->
        ${sectionParagraph(
          'Großartig, Dein erster großer Schritt hin zu einem gesünderen "Ich" ist fast geschafft! Um diesen Schritt abzuschließen, bestätige bitte Dein Leistungsniveau:'
        )}

        <!-- Akkordion -->
        ${accordion({
          title: "Anderes Leistungsniveau wählen",
          text: "Wenn Du möchtest, kannst Du auch ein anderes Leistungsniveau wählen. Bitte bedenke, dass es dadurch schwieriger für Dich werden könnte, Dein Gesundheitsziel zu erreichen.",
          buttonLabel: `Weiter mit ${alternativeLevelText}`,
          buttonUrl: "#", // Dummy URL, da wir den Event-Listener verwenden
          badge: null,
          open: isAccordionOpen,
          onToggle: toggleAccordion,
        })}

        <!-- Button -->
        <div class="button-container content-padding button-bottom">
          ${buttonStandard({
            text: `Weiter mit ${levelText}`,
            variant: "primary",
            id: "button-complete",
          })}
        </div>
      </div>
    `;
  }
  const step = steps.find((s) => s.id === currentStep);

  if (!step) {
    console.error(`Step ${currentStep} not found!`);
    return html`<p>Ein Fehler ist aufgetreten. Bitte lade die Seite neu.</p>`;
  }

  return html`
    <!-- Top Menu -->
    ${templateTopMenu()}
    <!-- Content -->
    <div class="niveau-poll-container content-top-padding">
      <div class="content-padding standard-container content-left-align">
        ${renderProgressBar(currentStep)}
      </div>
      <!-- Überschrift -->
      <h2 class="step-title green-text content-padding">${step.title}</h2>
      <!-- Poll -->
      ${step.description
        ? html`
            <p
              class="instruction-text ${currentStep === 2
                ? "green-text content-padding"
                : ""}"
            >
              ${step.description}
            </p>
            <!-- Button für Step 2 -->
            ${currentStep === 2
              ? html`<div class="content-padding">
                  ${buttonTextIcon(
                    "Zur Übungsanleitung",
                    iconArrowRight,
                    "right"
                  )}
                </div>`
              : ""}
            <!-- Bild nur für Step 2 -->
            ${currentStep === 2
              ? html`<img src="${imgLegs}" alt="Beine" class="poll-image" />`
              : ""}
          `
        : radioPoll({ name: `step-${currentStep}`, options: step.options })}
      <!-- Zusatztext in Step 1-->
      ${currentStep === 1
        ? html`<div class="content-padding content-top-padding">
            ${buttonTextIcon(
              'Was bedeutet "aktiv" sein?',
              iconArrowRight,
              "right"
            )}
          </div>`
        : ""}
      <!-- Button -->
      <div class="button-container content-padding button-bottom">
        ${buttonStandard({
          text:
            currentStep === steps.length
              ? "Abschließen"
              : "Zum nächsten Schritt",
          variant: "primary",
          disabled: true,
          id: `button-step-${currentStep}`,
        })}
      </div>
    </div>
  `;
};

const updateUI = () => {
  cleanupEventListeners(); // Alte Listener entfernen

  // Finde den content-wrapper innerhalb des page-content
  const contentWrapper = document.querySelector('.page-content .app-content-wrapper');
  if (contentWrapper) {
    render(renderStepContent(), contentWrapper);
    setupEventListeners(); // Neue Listener hinzufügen
  } else {
    console.error("Content wrapper not found!");
  }
};

/*
  ====================
  ÖFFENTLICHE SCHNITTSTELLE
  ====================
*/
export const templateNiveauPoll = () => {
  // Initialen State zurücksetzen beim ersten Aufruf
  currentStep = 1;
  pollAnswers = {};
  calculatedLevel = null;
  isAccordionOpen = false;

  // Verzögertes Rendering, damit der Router zuerst die Seite wechseln kann
  setTimeout(() => {
    updateUI();
  }, 0);

  // Leeres Template zurückgeben, damit der Router die Struktur erstellt
  return html``;
};
