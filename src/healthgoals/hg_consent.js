import { html } from "lit-html";
import { sectionTitle } from "../../webcomponents/sectionTitle.js";
import { sectionParagraph } from "../../webcomponents/sectionParagraph.js";
import { topMenu } from "../../webcomponents/topMenu.js";
import iconArrowLeft from "../../svg/icons/ArrowLeft.svg";
import iconArrowRight from "../../svg/icons/ArrowRight.svg";
import { checkbox } from "../../webcomponents/checkbox.js";
import { sectionNumberedList } from "../../webcomponents/sectionNumberedList.js";
import { buttonTextIcon } from "../../webcomponents/buttonTextIcon.js";
import { buttonStandard } from "../../webcomponents/buttonStandard.js";
import { router, exitWithSlide } from "../../router.js"; // Importiere exitWithSlide
import { logError, trySafe, ErrorLevel } from "../../utils.js";

/**
 * Creates the top menu template for the consent page
 * @returns {TemplateResult} The top menu template
 * @private
 */
const templateTopMenu = () => html`
  ${topMenu({
    backIcon: iconArrowLeft,
    primaryIcon: "",
    menuIcon: "",
    onBack: () => exitWithSlide("/healthgoals-overview/hg-fitUmgebung") // Füge exitWithSlide hinzu
  })}
`;

/**
 * Scrollt animiert zum Weiter-Button
 */
const scrollToButton = () => {
  setTimeout(() => {
    const button = document.querySelector('#startNiveau');
    if (button) {
      button.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, 300); // Kurze Verzögerung für bessere UX
};


/**
 * Handles checkbox change events
 * @param {HTMLInputElement} checkboxElement - The checkbox element
 * @param {HTMLButtonElement} buttonElement - The button element to enable/disable
 * @private
 */
const handleCheckboxChange = (checkboxElement, buttonElement) => {
  trySafe(() => {
    if (checkboxElement.checked) {
      buttonElement.removeAttribute("disabled");
      buttonElement.classList.remove("disabled");
      logError("Consent checkbox checked", ErrorLevel.INFO);
      
      // Scrolle zum Button nach kurzer Verzögerung
      scrollToButton();
    } else {
      buttonElement.setAttribute("disabled", "true");
      buttonElement.classList.add("disabled");
      logError("Consent checkbox unchecked", ErrorLevel.INFO);
    }
  }, [], "Error handling checkbox change", ErrorLevel.WARNING);
};


/**
 * Handles button click events
 * @param {HTMLButtonElement} buttonElement - The button element
 * @private
 */
const handleButtonClick = (buttonElement) => {
  trySafe(() => {
    if (!buttonElement.hasAttribute("disabled")) {
      logError("Navigating to initial level assessment", ErrorLevel.INFO);
      router.navigate("/hg-niveau-initial");
    } else {
      logError("Button click attempted while disabled", ErrorLevel.WARNING);
    }
  }, [], "Error handling button click", ErrorLevel.WARNING);
};

/**
 * Sets up event listeners for the consent form
 * @param {string} checkboxId - The ID of the checkbox element
 * @param {string} buttonId - The ID of the button element
 * @private
 */
const setupEventListeners = (checkboxId, buttonId) => {
  trySafe(() => {
    setTimeout(() => {
      const checkboxElement = document.getElementById(checkboxId);
      const buttonElement = document.getElementById(buttonId);

      if (!checkboxElement) {
        throw new Error(`Checkbox element with ID '${checkboxId}' not found`);
      }

      if (!buttonElement) {
        throw new Error(`Button element with ID '${buttonId}' not found`);
      }

      // Set up checkbox change event
      checkboxElement.addEventListener("change", () =>
        handleCheckboxChange(checkboxElement, buttonElement)
      );

      // Set up button click event
      buttonElement.addEventListener("click", () =>
        handleButtonClick(buttonElement)
      );

      logError("Consent form event listeners set up successfully", ErrorLevel.INFO);
    }, 0);
  }, [], "Failed to set up consent form event listeners", ErrorLevel.ERROR);
};

/**
 * Renders the consent form template for health goals
 * This template allows users to consent to data processing for health goals
 * and enables them to proceed to the next step once they've given consent.
 *
 * @returns {TemplateResult} The consent form template
 */
export const templateConsent = () => {
  console.log("[templateConsent] Rendering consent template");
  
  // Reset scroll position with a slight delay to ensure the DOM is ready
  setTimeout(() => {
    const appContainer = document.querySelector(".phone-content");
    if (appContainer) {
      console.log(`[templateConsent] Setting scroll position to 0 (was: ${appContainer.scrollTop})`);
      appContainer.scrollTop = 0;
    } else {
      console.log(`[templateConsent] Could not find .phone-content element`);
    }
  }, 0);

  // Element IDs
  const checkboxId = "consentCheckbox";
  const buttonId = "startNiveau";

  // Set up event listeners
  setupEventListeners(checkboxId, buttonId);

  return html`
    <!-- Top Menu -->
    ${templateTopMenu()}

    <!-- Content -->
    ${sectionTitle("Einwilligung")}

    ${sectionParagraph(
      'Um das Feature "Meine Ziele" nutzen zu können, benötigen wir Deine Zustimmung zu den dabei stattfindenden Datenverarbeitungen. Bitte lies die Datenschutz- und Einwilligungserklärung aufmerksam durch.'
    )}

    ${sectionParagraph(
      "Du kannst Deine Zustimmung in den Einstellungen jederzeit widerrufen."
    )}

    <div class="blue-bg-box content-padding content-top-padding">
      <!-- Checkbox -->
      ${checkbox(checkboxId, "Gesundheitsziele zu Sport & Bewegung aktivieren")}

      <div class="content-top-padding left-indent caption-container">
        ${sectionParagraph(
          "Ich möchte von der App unterstützt werden, meine selbst festgelegten Gesundheitsziele zu erreichen, und damit Bonuspunkte im Rahmen des AOK PLUS Bonusprogramms sammeln."
        )}

        ${sectionParagraph("Dazu willige ich ein, dass")}

        ${sectionNumberedList([
          "die AOK PLUS die im Rahmen meiner Selbsteinschätzung erhobenen Daten inklusive meines Körpergewichtes verwendet, um mein Fitnesslevel festzustellen sowie mein Körpergewicht zusammen mit meiner Partner-ID (Pseudonym) direkt und unmittelbar an die mHealth Pioneers GmbH weiterleitet, damit diese meine MET-Werte eigenverantwortlich berechnet und das jeweilige Ergebnis mir zugeordnet werden kann. Eine Speicherung meiner Selbsteinschätzung durch die AOK PLUS findet nicht statt.",
          "die mHealth Pioneers GmbH (\"mHealth\") meine/n Fitness-Tracker/ Fitness App mit der Plattform von mHealth verbinden darf, damit sie erheben kann, welchen Typ von Aktivität wie z.B. Laufen, Rad fahren, etc. ich durchgeführt habe und wie viele Kalorien ich dabei in welcher Zeit verbrannt habe. Weiterhin darf mHealth meine erreichten MET-Werte zusammen mit dem Zeitfenster, in dem ich diese erzielt habe, an die AOK PLUS übermitteln, damit diese mir den jeweils aktuellen Stand meiner Challenge in der App anzeigen kann.",
        ])}
      </div>

      <div class="content-top-padding content-bottom-padding">
        ${buttonTextIcon("Datenschutzerklärung AOK PLUS", iconArrowRight)}
        ${buttonTextIcon("Datenschutzerklärung mHealth", iconArrowRight)}
      </div>
    </div>

    <div class="content-padding">
      <!-- Weiter Button -->
      ${buttonStandard({
        text: "Weiter",
        variant: "primary",
        disabled: true, // Button ist standardmäßig deaktiviert
        id: buttonId,
        ariaLabel: "Zum nächsten Schritt"
      })}
    </div>
  `;
};
