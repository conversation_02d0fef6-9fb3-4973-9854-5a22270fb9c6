import { html } from "lit-html";
import aok<PERSON><PERSON> from "../svg/AOK Logo.svg";
import navida<PERSON>ogo from "../svg/Navida Logo.svg";
import { logError, trySafe, validateInput, ErrorLevel, appStorage } from "../utils.js";

// Default username if none is provided
const DEFAULT_USERNAME = "Nutzer";

/**
 * Welcome screen template with login functionality
 * @returns {TemplateResult} The welcome screen template
 */
export const templateAppstart01 = () => html`
  <div class="content-padding content-widening standard-container">
    <img src="${aokLogo}" alt="AOK Logo" class="startAnimation" />
    <img src="${navidaLogo}" alt="NAVIDA Logo" class="startAnimation" />
  </div>
  <div class="content-padding content-widening standard-container">
    <p>Bitte gib Deinen Namen ein:</p>
    <input
      id="nameInput"
      class="on-bg"
      type="text"
      @input=${(e) => setUserName(e.target.value)}
      @keydown=${handleKeydown}
      placeholder="${DEFAULT_USERNAME}"
      aria-label="Name Eingabe"
    />
    <div id="nameError" class="error-message" style="display: none; color: red; font-size: 0.8rem; margin-top: 4px;"></div>
    <button
      id="submitButton"
      class="full-width"
      data-navigate="/homescreen"
      @click=${handleSubmit}
      aria-label="Login Button"
    >
      Anmelden
    </button>
  </div>
`;

/**
 * Sets the user name based on input value
 * @param {string} value - The input value
 * @returns {void}
 */
function setUserName(value) {
  trySafe(() => {
    const inputField = document.querySelector("#nameInput");
    const errorElement = document.querySelector("#nameError");

    if (!inputField) {
      throw new Error("Name-Eingabefeld nicht gefunden");
    }

    // Validate the input
    const validation = validateInput(inputField.value, {
      maxLength: 50,
      pattern: /^[a-zA-Z0-9\s\-_.]+$/,
      customValidator: (val) => {
        return !val.includes("@") || "Name sollte kein @-Symbol enthalten";
      }
    });

    // Show validation error if any
    if (errorElement) {
      if (!validation.isValid) {
        errorElement.textContent = validation.message;
        errorElement.style.display = "block";
      } else {
        errorElement.style.display = "none";
      }
    }

    // Set the username
    if (inputField.value.trim() === "") {
      window.userName = DEFAULT_USERNAME;
    } else {
      window.userName = inputField.value;
    }
  }, [], "Failed to set user name", ErrorLevel.WARNING);
}

/**
 * Handles the keydown event for the name input
 * @param {KeyboardEvent} event - The keyboard event
 * @returns {void}
 */
function handleKeydown(event) {
  trySafe(() => {
    if (event.key === "Enter") {
      handleSubmit();
    }
  }, [], "Error handling keyboard input", ErrorLevel.INFO);
}

/**
 * Handles the submit button click
 * @returns {void}
 */
function handleSubmit() {
  trySafe(() => {
    const inputField = document.querySelector("#nameInput");
    const errorElement = document.querySelector("#nameError");

    if (!inputField) {
      throw new Error("Name input field not found");
    }

    // Validate the input
    const validation = validateInput(inputField.value, {
      maxLength: 50,
      pattern: /^[a-zA-Z0-9\s\-_.]+$/,
      customValidator: (val) => {
        return !val.includes("@") || "Name sollte kein @-Symbol enthalten";
      }
    });

    // Show validation error if any
    if (errorElement) {
      if (!validation.isValid) {
        errorElement.textContent = validation.message;
        errorElement.style.display = "block";
        return; // Don't proceed if validation fails
      } else {
        errorElement.style.display = "none";
      }
    }

    // Set the username
    appStorage.userName = inputField.value.trim() || DEFAULT_USERNAME;

    // Log
    logError(`Nutzer angemeldet als: ${appStorage.userName}`, ErrorLevel.INFO);
  }, [], "Failed to submit login form", ErrorLevel.WARNING);
}

// Log module initialization
logError("Willkommens-Modul geladen", ErrorLevel.INFO);
