import { html } from "lit-html";
import home from "../svg/icons/icon_home.svg";
import homeFilled from "../svg/icons/icon_home_filled.svg";
import features from "../svg/icons/icon_features.svg";
import featuresFilled from "../svg/icons/icon_features_filled.svg";
import user from "../svg/icons/icon_user.svg";
import userFilled from "../svg/icons/icon_user_filled.svg";
import settings from "../svg/icons/icon_settings.svg";
import settingsFilled from "../svg/icons/icon_settings_filled.svg";

export const BottomNavigation = (activeItem) => html`
  <div class="bottom-menu" id="bottomMenu">
    <a
      class="menu-item ${activeItem === "home" ? "active" : ""}"
      data-navigate="/homescreen"
    >
      <img src="${activeItem === "home" ? homeFilled : home}" alt="Start" />
      <span>Start</span>
    </a>
    <a
      class="menu-item ${activeItem === "features" ? "active" : ""}"
      data-navigate="/features"
    >
      <img src="${activeItem === "features" ? featuresFilled : features}" alt="Funktionen" />
      <span>Funktionen</span>
    </a>
    <a
      class="menu-item ${activeItem === "dein-bereich" ? "active" : ""}"
      data-navigate="/dein-bereich"
    >
      <img src="${activeItem === "dein-bereich" ? userFilled : user}" alt="Dein Bereich" />
      <span>Dein Bereich</span>
    </a>
    <a
      class="menu-item ${activeItem === "settings" ? "active" : ""}"
      data-navigate="/settings"
    >
      <img src="${activeItem === "settings" ? settingsFilled : settings}" alt="Einstellungen" />
      <span>Einstellungen</span>
    </a>
  </div>
`;
