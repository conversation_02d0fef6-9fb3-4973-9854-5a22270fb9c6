:root {
  /* Colors */
  --primary-brand: var(--aok-grn-8); /* dark green */
  --primary-contrast: var(--aok-grn-4); /* light green contrast */
  --primary-light-grn: #50eb5d; /* light green */

  --interactive-color: var(--accent-green); /* interactive green */

  --primary-text: var(--aok-gray-9); /* Primary Text black */

  --button-primary-background-default: var(--accent-green);
  --button-text: var(--secondary-dark-green);

  /* Button Colors */
  --button-primary-background-pressed: var(--pressed-primary);
  --text-button-text: var(--aok-grn-8);

  --button-primary-background-default: var(--accent-green);
  --button-primary-background-pressed: var(--pressed-primary);
  --button-primary-background-disabled: var(--disabled-interactive-green);

  --button-secondary-on-backgound-default: var(--aok-grn-8);
  --button-secondary-on-backgound-pressed: var(--aok-grn-8);
  --button-secondary-background-pressed: var(--pressed-secondary);
  --button-secondary-on-backgound-disabled: var(--disabled-green);

  --button-tertiary-background-pressed: var(--pressed-secondary);

  --button-danger-background-default: var(--accent-error);
  --button-danger-background-pressed: #d1003c; /* Keine passende Variable vorhanden */

  /* Input Forms */
  --text-input-border-active: var(--aok-gray-6);
  --text-input-border-focus: var(--aok-grn-8);
  --text-input-text-active: var(--aok-gray-6);
  --text-input-text-focus: var(--aok-gray-9);

  /* Card */
  --cards-info-card-background-blue: var(--secondary-blue);
  --cards-text: var(--aok-gray-9);
  --feature-card-background-blue: var(--secondary-blue);
  --feature-card-background-green: var(--secondary-green);

  /* Icons */
  --icon-black: var(--aok-gray-9);

  /* Chips */
  --chip-background: var(--secondary-blue);
  --chip-text: var(--aok-grn-8);
  --chip-active-background: var(--aok-grn-8);
  --chip-active-text: white;

  /* Tags */
  --tag-text: var(--aok-gray-9);
  --tag-sleep: var(--aok-gray-1);
  --tag-sport: var(--secondary-sand);
  --tag-psych: var(--secondary-green);
  --tag-food: var(--secondary-blue);
  --tag-green: var(--secondary-green);
  --tag-text-green: var(--aok-grn-8);

  /* Pills */
  --pill-green-background: var(--secondary-green);
  --pill-green-text: var(--aok-grn-8);
  --pill-blue-background: var(--secondary-blue);
  --pill-blue-text: var(--aok-grn-8);
  --pill-accent-blue-background: var(--accent-blue);
  --pill-accent-blue-text: var(--aok-grn-8);

  /* Progress Bar */
  --bar-background: var(--aok-gray-1);
  --bar-fill: var(--aok-grn-8);

  /* Headlines */
  --headline-green: var(--aok-grn-8);
  --headline-black: var(--aok-gray-9);

  /* Bottom Navigation */
  --bottom-navigation-active: var(--aok-grn-8);

  /* Info Cards */
  --info-card-background-blue: var(--secondary-blue);
  --info-card-background-green: var(--secondary-green);
  --info-card-background-yellow: var(--secondary-sand);
  --info-card-background-red: var(--accent-error);

  --caption-black: var(--aok-gray-9);
  --caption-green: var(--aok-grn-8);

  /* Checkbox */
  --checkboxes-deselected-background: var(--aok-grn-8);
  --checkboxes-deselected-border: var(--aok-grn-8);
  --checkboxes-deselected-checked-background: var(--aok-grn-8);
  --checkboxes-deselected-checked-border: var(--aok-grn-8);
  --checkboxes-deselected-checked-icon: var(--aok-grn-8);
  --checkboxes-deselected-checked-icon-hover: var(--aok-grn-8);
  --list-item-text: var(--aok-gray-9);

  /* Radiobuttons */
  --radio-border-color: var(--aok-grn-8);
  --radio-text-color: var(--aok-gray-9);

  /* Dividers */
  --divider-default: var(--aok-gray-1);

  /* Dialog */
  --dialog-background: #FFF;
  --dialog-text: var(--aok-gray-9);
  --bubble-yellow: var(--accent-yellow);
  --bubble-blue: var(--accent-blue);
  --bubble-green: var(--accent-green);
  --bubble-red: var(--accent-error);

  /* green tones */
  --aok-grn-9: #0e3a2d;
  --aok-grn-8: #005e3f;
  --aok-grn-7: #007141;
  --aok-grn-6: #028440;
  --aok-grn-5: #07983e;
  --aok-grn-4: #18ab42;
  --aok-grn-3: #35bf50;
  --aok-grn-2: #60d26d;
  --aok-grn-1: #98e692;

  /* gray tones */
  --aok-gray-9: #293033;
  --aok-gray-8: #40484d;
  --aok-gray-7: #586167;
  --aok-gray-6: #6d767c;
  --aok-gray-5: #828b91;
  --aok-gray-4: #98a1a6;
  --aok-gray-3: #afb6bc;
  --aok-gray-2: #c7ccd1;
  --aok-gray-1: #dfe3e6;

  /* Accents */
  --accent-yellow: #fff133;
  --accent-blue: #83eaf2;
  --accent-green: #91f54a;
  --accent-error: #eb0047;
  --accent-orange: #d96629;

  /* Secondary Colors */
  --secondary-dark-green: #004730;
  --secondary-brightBlue: #c0f5f9;
  --secondary-sand: #f8f5e3;
  --secondary-blue: #e8f4f2;
  --secondary-green: #eefaea;

  /* Disabled Colors */
  --disabled-interactive-green: #d3fbb7;
  --disabled-green: #99b5ac;
  --disabled-grey: #b6bbbe;
  --disabled-interactive-elements: #99bfb2;
  --disabled-error: #f799b5;

  /* Pressed Colors */
  --pressed-primary: #80e349;
  --pressed-secondary: #e0ece8;

  /* --- */
  /* Sizes */
  --phone-screen-width: 375px;
  --phone-screen-height: 812px;

  --phone-header-height: 54px;
  --phone-footer-height: 32px;
}
